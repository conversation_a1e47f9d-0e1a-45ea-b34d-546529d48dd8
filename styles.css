/* General Body Styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
}

header {
    background: #333;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
}

header h1 {
    margin: 0;
    font-size: 2.5em;
}

header nav ul {
    padding: 0;
    list-style: none;
}

header nav ul li {
    display: inline;
    margin-right: 20px;
}

header nav ul li a {
    color: #fff;
    text-decoration: none;
    font-size: 1.1em;
}

header nav ul li a:hover {
    text-decoration: underline;
}

main {
    padding: 20px;
    max-width: 1200px;
    margin: 20px auto;
    background: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

section h2 {
    color: #333;
    border-bottom: 2px solid #333;
    padding-bottom: 10px;
    margin-top: 0;
}

section h3 {
    color: #555;
}

/* Text Element Styling */
#text-elements p {
    margin-bottom: 10px;
}

#text-elements span {
    background-color: yellow;
    padding: 2px 4px;
}

#text-elements strong { font-weight: bold; }
#text-elements em { font-style: italic; }
#text-elements u { text-decoration: underline; }
#text-elements mark { background-color: #ff0; color: #000; }
#text-elements small { font-size: 0.8em; }
#text-elements del { text-decoration: line-through; color: red; }
#text-elements ins { text-decoration: none; background-color: lightgreen; }
#text-elements sub { vertical-align: sub; font-size: 0.75em; }
#text-elements sup { vertical-align: super; font-size: 0.75em; }
#text-elements q { quotes: "\201C" "\201D" "\2018" "\2019"; }
#text-elements q::before { content: open-quote; }
#text-elements q::after { content: close-quote; }
#text-elements blockquote {
    border-left: 5px solid #ccc;
    margin-left: 20px;
    padding-left: 15px;
    font-style: italic;
}
#text-elements blockquote cite {
    display: block;
    text-align: right;
    margin-top: 5px;
    font-style: normal;
}
#text-elements abbr[title] {
    text-decoration: underline dotted;
    cursor: help;
}
#text-elements address {
    font-style: italic;
    margin-bottom: 1em;
}
#text-elements pre {
    background-color: #eee;
    border: 1px solid #ddd;
    padding: 10px;
    overflow-x: auto; /* For long code lines */
}
#text-elements code {
    font-family: 'Courier New', Courier, monospace;
}
#text-elements dfn {
    font-style: italic;
    font-weight: bold;
}
#text-elements kbd {
    background-color: #eee;
    border-radius: 3px;
    border: 1px solid #b4b4b4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .2), 0 2px 0 0 rgba(255, 255, 255, .7) inset;
    color: #333;
    display: inline-block;
    font-size: .85em;
    font-weight: 700;
    line-height: 1;
    padding: 2px 4px;
    white-space: nowrap;
}
#text-elements samp {
    font-family: monospace;
    background-color: #f0f0f0;
    padding: 1px 3px;
    border: 1px solid #ccc;
}
#text-elements var {
    font-style: italic;
    font-family: serif;
}

/* List Element Styling */
#list-elements ul, #list-elements ol {
    padding-left: 20px;
}
#list-elements ul ul {
    list-style-type: circle;
    margin-top: 5px;
}
#list-elements ol {
    list-style-type: decimal;
}
#list-elements dl dt {
    font-weight: bold;
    margin-top: 10px;
}
#list-elements dl dd {
    margin-left: 20px;
}

/* Media Element Styling */
#media-elements img, #media-elements video, #media-elements audio {
    max-width: 100%;
    height: auto;
    display: block;
    margin-bottom: 10px;
}
#media-elements figure {
    margin: 0 0 1em 0;
    border: 1px solid #ccc;
    padding: 10px;
    display: inline-block; /* Or block, depending on layout needs */
}
#media-elements figcaption {
    font-style: italic;
    text-align: center;
    margin-top: 5px;
    font-size: 0.9em;
}

/* Form Element Styling */
#form-elements form div {
    margin-bottom: 15px;
}
#form-elements label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
#form-elements input[type="text"],
#form-elements input[type="email"],
#form-elements input[type="password"],
#form-elements input[type="tel"],
#form-elements input[type="url"],
#form-elements input[type="search"],
#form-elements input[type="number"],
#form-elements input[type="date"],
#form-elements input[type="time"],
#form-elements input[type="datetime-local"],
#form-elements input[type="month"],
#form-elements input[type="week"],
#form-elements textarea,
#form-elements select {
    width: calc(100% - 22px); /* Account for padding and border */
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

#form-elements input[type="color"] {
    width: 100px;
    height: 40px;
    padding: 5px;
}

#form-elements input[type="range"] {
    width: calc(100% - 60px); /* Adjust width to make space for output */
    vertical-align: middle;
}

#form-elements output {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
}

#form-elements fieldset {
    border: 1px solid #ccc;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

#form-elements legend {
    font-weight: bold;
    padding: 0 10px;
    color: #333;
}

#form-elements input[type="radio"],
#form-elements input[type="checkbox"] {
    margin-right: 5px;
    vertical-align: middle;
}

#form-elements button,
#form-elements input[type="submit"],
#form-elements input[type="reset"],
#form-elements input[type="button"] {
    background-color: #337ab7;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    margin-right: 10px;
}

#form-elements button:hover,
#form-elements input[type="submit"]:hover,
#form-elements input[type="reset"]:hover,
#form-elements input[type="button"]:hover {
    background-color: #286090;
}

#form-elements input:required {
    border-left: 3px solid red;
}

#form-elements input:optional {
    border-left: 3px solid green;
}

#form-elements input:focus {
    outline: none;
    border-color: #337ab7;
    box-shadow: 0 0 5px rgba(51, 122, 183, 0.5);
}

/* Table Element Styling */
#table-elements table {
    width: 100%;
    border-collapse: collapse; /* Removes double borders */
    margin-bottom: 20px;
}

#table-elements caption {
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 10px;
    caption-side: top; /* or bottom */
}

#table-elements th, #table-elements td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

#table-elements th {
    background-color: #f0f0f0;
    font-weight: bold;
}

#table-elements tbody tr:nth-child(even) { /* Zebra striping for rows */
    background-color: #f9f9f9;
}

#table-elements tbody tr:hover {
    background-color: #f1f1f1;
}

#table-elements tfoot td {
    font-weight: bold;
    background-color: #e0e0e0;
}

/* Semantic Element Styling */
#semantic-elements article {
    background-color: #eef;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 5px solid #aac;
}

#semantic-elements aside {
    background-color: #ffe;
    padding: 15px;
    margin: 15px 0;
    border-left: 5px solid #cca;
    width: 30%;
    float: right; /* Example of float, clear floats if needed */
}

/* Clearfix for floated elements like aside if content after it should not wrap */
#semantic-elements section:after {
    content: "";
    display: table;
    clear: both;
}

#semantic-elements details summary {
    cursor: pointer;
    font-weight: bold;
}

#semantic-elements details p {
    padding-left: 20px;
}

#semantic-elements time {
    font-style: italic;
    color: #555;
}

#semantic-elements progress {
    width: 100%;
    height: 20px;
    margin-bottom: 10px;
}

#semantic-elements meter {
    width: 100%;
    height: 20px;
}
/* Styling meter based on its values */
#semantic-elements meter::-webkit-meter-bar {
    background: #eee;
    border-radius: 3px;
}
#semantic-elements meter::-webkit-meter-optimum-value {
    background: green;
}
#semantic-elements meter::-webkit-meter-suboptimum-value {
    background: orange;
}
#semantic-elements meter::-webkit-meter-even-less-good-value {
    background: red;
}

/* Flexbox Container Section */
.flexbox-container-section .flex-container {
    display: flex; /* Establishes a flex container */
    /* flex-direction: row; (default) | row-reverse | column | column-reverse */
    flex-direction: row;
    /* flex-wrap: nowrap; (default) | wrap | wrap-reverse */
    flex-wrap: wrap;
    /* justify-content: flex-start; (default) | flex-end | center | space-between | space-around | space-evenly */
    justify-content: space-around;
    /* align-items: stretch; (default) | flex-start | flex-end | center | baseline */
    align-items: stretch; 
    /* align-content: stretch; (default) | flex-start | flex-end | center | space-between | space-around */
    /* (only applies if there are multiple lines of flex items due to wrapping) */
    align-content: flex-start;
    background-color: dodgerblue;
    padding: 10px;
    border: 2px solid #333;
    height: 300px; /* Fixed height to demonstrate align-content */
}

.flexbox-container-section .flex-item {
    background-color: #f1f1f1;
    border: 1px solid #ccc;
    padding: 20px;
    margin: 10px;
    text-align: center;
    font-size: 1.2em;
    /* flex-grow: 0; (default) - how much the item will grow relative to other items */
    /* flex-shrink: 1; (default) - how much the item will shrink relative to other items */
    /* flex-basis: auto; (default) - initial size of the item */
    /* flex: <flex-grow> <flex-shrink> <flex-basis>; (shorthand) */
}

.flexbox-container-section .item1 {
    flex-grow: 1;
    order: 1; /* Change order of items */
}

.flexbox-container-section .item2 {
    flex-grow: 2; /* Will take twice as much space as item1 if available */
    align-self: center; /* Override align-items for this specific item */
    order: 0;
}

.flexbox-container-section .item3 {
    flex-basis: 200px; /* Initial width of 200px */
    flex-shrink: 0; /* Won't shrink */
    order: 2;
}

/* Grid Container Section */
.grid-container-section .grid-container {
    display: grid; /* Establishes a grid container */
    /* grid-template-columns: auto auto auto; (defines columns) */
    /* Can use fr units (fractional units), px, %, etc. */
    grid-template-columns: repeat(3, 1fr); /* Three equal-width columns */
    /* grid-template-rows: auto auto; (defines rows) */
    grid-template-rows: auto 150px auto;
    /* gap: <row-gap> <column-gap>; (shorthand for grid-row-gap and grid-column-gap) */
    gap: 15px;
    background-color: #2196F3;
    padding: 10px;
    border: 2px solid #333;
}

.grid-container-section .grid-item {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.8);
    padding: 20px;
    font-size: 1.2em;
    text-align: center;
}

/* Spanning grid items */
.grid-container-section .gitem1 {
    /* grid-column-start: 1; */
    /* grid-column-end: 4; (spans from line 1 to line 4, i.e., 3 columns) */
    grid-column: 1 / span 3; /* Shorthand: start line / how many columns to span */
    /* grid-row-start: 1; */
    /* grid-row-end: 2; */
    grid-row: 1 / 2;
}

.grid-container-section .gitem2 {
    grid-column: 1 / 2;
    grid-row: 2 / span 2; /* Spans from row line 2 for 2 rows */
    /* align-self: start | end | center | stretch; */
    align-self: center;
    /* justify-self: start | end | center | stretch; */
    justify-self: center;
}

.grid-container-section .gitem3 {
    grid-column: 2 / span 2;
    grid-row: 2 / 3;
}

.grid-container-section .gitem4 {
    grid-column: 2 / span 2; /* Or 2 / 4 */
    grid-row: 3 / 4;
}

/* Naming grid areas (alternative to line numbers) */
.grid-container-section .grid-container-named-areas {
    display: grid;
    grid-template-areas:
        'header header header'
        'sidebar main main'
        'footer footer footer';
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 10px;
    margin-top: 30px;
    background-color: #4CAF50;
    padding: 10px;
}

.grid-container-section .grid-item-named {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #666;
    padding: 15px;
    text-align: center;
}

.grid-container-section .gitem-header { grid-area: header; }
.grid-container-section .gitem-sidebar { grid-area: sidebar; }
.grid-container-section .gitem-main { grid-area: main; }
.grid-container-section .gitem-footer { grid-area: footer; }


/* Footer Styles */
footer {
    text-align: center;
    padding: 20px;
    background: #333;
    color: #fff;
    margin-top: 30px;
}

footer p {
    margin: 5px 0;
}

footer a {
    color: #77abff;
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Pseudo-classes and Pseudo-elements Examples */

/* Link states */
a:link { /* Unvisited link */
    /* color: blue; (already set in header/footer) */
}
a:visited { /* Visited link */
    /* color: purple; */
}
a:hover { /* Mouse over link */
    /* text-decoration: underline; (already set) */
    opacity: 0.8;
}
a:active { /* Selected link */
    color: red;
}

/* Input pseudo-classes */
input:disabled {
    background-color: #eee;
    cursor: not-allowed;
}
input:checked + label { /* Style label next to checked checkbox/radio */
    font-weight: bold;
    color: green;
}

/* Structural pseudo-classes */
#list-elements ul li:first-child {
    color: green;
}
#list-elements ul li:last-child {
    color: red;
}
#list-elements ul li:nth-child(odd) {
    /* background-color: #f0f0f0; Example, might conflict with other styles */
}
#list-elements ul li:nth-child(even) {
    /* background-color: #e0e0e0; */
}

/* Pseudo-elements */
section h2::before {
    content: "⦿ ";
    color: #337ab7;
}

section p::first-line {
    /* font-weight: bold; (can be too much) */
}

section p::first-letter {
    /* font-size: 1.5em; */
    /* float: left; */
    /* margin-right: 5px; */
}

section p::selection {
    background-color: #337ab7;
    color: white;
}

/* Attribute Selectors */
input[type="text"] {
    /* border: 2px solid blue; (already styled, example) */
}
a[target="_blank"]::after {
    content: " ↗"; /* Indicate external link */
}
img[alt~="placeholder"] {
    border: 3px dashed orange;
}

/* CSS Variables (Custom Properties) */
:root {
    --main-bg-color: #f0f8ff;
    --main-text-color: #333;
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --padding-standard: 15px;
}

.css-variables-example {
    background-color: var(--main-bg-color);
    color: var(--main-text-color);
    padding: var(--padding-standard);
    border: 2px solid var(--primary-color);
    margin-top: 20px;
}

.css-variables-example h3 {
    color: var(--primary-color);
}

.css-variables-example p {
    color: var(--secondary-color);
}

/* Advanced Selectors */
/* Child combinator: selects direct children */
main > section {
    /* border-left: 5px solid gold; Example */
}

/* Adjacent sibling combinator: selects element immediately preceded by former */
section h2 + p {
    /* font-style: italic; Example */
}

/* General sibling combinator: selects all siblings that come after former */
section h2 ~ p {
    /* color: gray; Example */
}

/* Universal selector (use sparingly) */
* {
    box-sizing: border-box; /* Common reset, already applied to inputs */
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
    header nav ul li {
        display: block;
        margin-bottom: 10px;
    }

    #semantic-elements aside {
        width: 100%;
        float: none;
        margin-top: 15px;
    }

    .flexbox-container-section .flex-container {
        flex-direction: column; /* Stack items vertically on smaller screens */
        height: auto; /* Allow height to adjust to content */
    }

    .grid-container-section .grid-container {
        grid-template-columns: 1fr; /* Single column layout */
        grid-template-rows: auto; /* Rows adjust to content */
    }

    .grid-container-section .gitem1,
    .grid-container-section .gitem2,
    .grid-container-section .gitem3,
    .grid-container-section .gitem4 {
        grid-column: auto; /* Reset column spans */
        grid-row: auto; /* Reset row spans */
    }
}

@media (max-width: 480px) {
    body {
        font-size: 14px;
    }

    header h1 {
        font-size: 2em;
    }

    main {
        padding: 10px;
        margin: 10px;
    }

    #form-elements input[type="text"],
    #form-elements input[type="email"],
    #form-elements input[type="password"],
    #form-elements textarea,
    #form-elements select {
        width: calc(100% - 18px); /* Adjust for smaller padding */
        padding: 8px;
    }
}

/* Print-specific styles */
@media print {
    body {
        background-color: #fff;
        color: #000;
        font-family: 'Times New Roman', Times, serif;
    }

    header, footer, nav, aside, .flexbox-container-section, .grid-container-section {
        display: none; /* Hide non-essential elements for printing */
    }

    main {
        box-shadow: none;
        margin: 0;
        padding: 0;
    }

    section {
        border: none;
        page-break-inside: avoid;
    }

    a {
        text-decoration: none;
        color: #000;
    }

    a[href^="http"]::after {
        content: " (" attr(href) ")"; /* Show full URLs for external links */
    }
}