<?php
// ===== DATABASE CONFIGURATION =====
// Define constants for database connection parameters
// These constants will be used in db.php to establish a connection
define('DB_HOST', 'localhost');     // Database server address (usually localhost for local development)
define('DB_NAME', 'test_db');       // Name of the database to connect to
define('DB_USER', 'root');          // MySQL username (default is 'root' for local installations)
define('DB_PASS', '');              // MySQL password (empty by default for local installations)
define('DB_CHARSET', 'utf8mb4');    // Character set for database connection (utf8mb4 supports all Unicode characters including emojis)

// ===== ERROR REPORTING CONFIGURATION =====
// These settings are useful during development but should be turned off in production
ini_set('display_errors', 1);           // Show PHP errors on the page (1 = on, 0 = off)
ini_set('display_startup_errors', 1);   // Show PHP startup errors on the page
error_reporting(E_ALL);                 // Report all types of errors (E_ALL is the most comprehensive level)

// ===== SESSION SECURITY CONFIGURATION =====
// These settings enhance the security of PHP sessions
ini_set('session.cookie_httponly', 1);  // Prevent JavaScript access to session cookie (helps prevent XSS attacks)
ini_set('session.use_only_cookies', 1); // Force sessions to only use cookies (not URL parameters)
ini_set('session.cookie_secure', 0);    // Set to 1 if using HTTPS to ensure cookies are only sent over secure connections
ini_set('session.cookie_samesite', 'Strict'); // Prevent CSRF attacks by restricting cookies to same site
ini_set('session.gc_maxlifetime', 3600); // Session timeout in seconds (1 hour) - how long a session can be inactive before it expires

// ===== SESSION INITIALIZATION =====
// Start a new session if one doesn't already exist
// PHP_SESSION_NONE is a constant that indicates no session is active
if (session_status() === PHP_SESSION_NONE) {
    session_start();  // Initialize the session and create a session ID
}
?>