<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive HTML and CSS examples">
    <meta name="keywords" content="HTML, CSS, Flexbox, Grid, Web Development">
    <meta name="author" content="AI Assistant">
    <title>HTML & CSS Comprehensive Guide</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>HTML & CSS Comprehensive Guide</h1>
        <nav>
            <ul>
                <li><a href="#text-elements">Text Elements</a></li>
                <li><a href="#list-elements">Lists</a></li>
                <li><a href="#media-elements">Media</a></li>
                <li><a href="#form-elements">Forms</a></li>
                <li><a href="#table-elements">Tables</a></li>
                <li><a href="#semantic-elements">Semantic HTML</a></li>
                <li><a href="#flexbox-container">Flexbox</a></li>
                <li><a href="#grid-container">Grid</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="text-elements">
            <h2>Text Elements</h2>
            <p>This is a paragraph. <span>This is a span inside a paragraph.</span></p>
            <p><strong>This text is strong (bold).</strong> <em>This text is emphasized (italic).</em></p>
            <p><u>This text is underlined.</u> <mark>This text is marked.</mark></p>
            <p><small>This is small text.</small> <del>This text is deleted.</del> <ins>This text is inserted.</ins></p>
            <p>H<sub>2</sub>O (subscript) and E=MC<sup>2</sup> (superscript).</p>
            <p>This is a <q>short inline quotation</q>.</p>
            <blockquote>
                <p>This is a blockquote. It's used for longer quotations that are set off from the main text.</p>
                <cite>Source</cite>
            </blockquote>
            <abbr title="HyperText Markup Language">HTML</abbr>
            <address>
                Written by AI Assistant<br>
                Visit us at:<br>
                example.com<br>
                Box 564, Disneyland<br>
                USA
            </address>
            <pre>
                <code>
function greet() {
  console.log("Hello, world!");
}
                </code>
            </pre>
            <p>The <dfn>definition element</dfn> is used to indicate the defining instance of a new term.</p>
            <p>My favorite color is <del>blue</del> <ins>red</ins>!</p>
            <p>Press <kbd>Ctrl</kbd> + <kbd>C</kbd> to copy text.</p>
            <p>The program is generating <samp>Sample output from a computer program</samp>.</p>
            <p>The variable <var>x</var> represents the unknown quantity.</p>
        </section>

        <section id="list-elements">
            <h2>List Elements</h2>
            <h3>Unordered List</h3>
            <ul>
                <li>Item 1</li>
                <li>Item 2
                    <ul>
                        <li>Sub-item 2.1</li>
                        <li>Sub-item 2.2</li>
                    </ul>
                </li>
                <li>Item 3</li>
            </ul>
            <h3>Ordered List</h3>
            <ol start="5">
                <li>First item (starts at 5)</li>
                <li>Second item</li>
                <li>Third item</li>
            </ol>
            <h3>Description List</h3>
            <dl>
                <dt>HTML</dt>
                <dd>HyperText Markup Language</dd>
                <dt>CSS</dt>
                <dd>Cascading Style Sheets</dd>
            </dl>
        </section>

        <section id="media-elements">
            <h2>Media Elements</h2>
            <h3>Image</h3>
            <img src="https://via.placeholder.com/150" alt="Placeholder Image" width="150" height="150">
            
            <h3>Figure with Caption</h3>
            <figure>
                <img src="https://via.placeholder.com/300x200" alt="Another Placeholder Image">
                <figcaption>Fig.1 - A placeholder image with a caption.</figcaption>
            </figure>

            <h3>Audio</h3>
            <audio controls>
                <source src="#" type="audio/mpeg"> <!-- Replace # with actual audio file -->
                Your browser does not support the audio element.
            </audio>

            <h3>Video</h3>
            <video width="320" height="240" controls poster="https://via.placeholder.com/320x240/000000/FFFFFF/?text=Video+Poster">
                <source src="#" type="video/mp4"> <!-- Replace # with actual video file -->
                <track label="English" kind="subtitles" srclang="en" src="#" default> <!-- Replace # with actual subtitles file -->
                Your browser does not support the video tag.
            </video>
        </section>

        <section id="form-elements">
            <h2>Form Elements</h2>
            <form action="#" method="post">
                <fieldset>
                    <legend>Personal Information</legend>
                    <div>
                        <label for="name">Name:</label>
                        <input type="text" id="name" name="username" required placeholder="Enter your name">
                    </div>
                    <div>
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="useremail" required placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="userpassword" required minlength="8">
                    </div>
                    <div>
                        <label for="tel">Telephone:</label>
                        <input type="tel" id="tel" name="usertel" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************">
                    </div>
                    <div>
                        <label for="url">Website:</label>
                        <input type="url" id="url" name="userurl" placeholder="https://example.com">
                    </div>
                    <div>
                        <label for="search">Search:</label>
                        <input type="search" id="search" name="usersearch">
                    </div>
                    <div>
                        <label for="number">Number (1-10):</label>
                        <input type="number" id="number" name="usernumber" min="1" max="10" step="1">
                    </div>
                    <div>
                        <label for="range">Range (0-100):</label>
                        <input type="range" id="range" name="userrange" min="0" max="100" value="50">
                        <output for="range" id="range-output">50</output>
                    </div>
                    <div>
                        <label for="date">Date:</label>
                        <input type="date" id="date" name="userdate">
                    </div>
                    <div>
                        <label for="time">Time:</label>
                        <input type="time" id="time" name="usertime">
                    </div>
                    <div>
                        <label for="datetime-local">Date and Time:</label>
                        <input type="datetime-local" id="datetime-local" name="userdatetime">
                    </div>
                    <div>
                        <label for="month">Month:</label>
                        <input type="month" id="month" name="usermonth">
                    </div>
                    <div>
                        <label for="week">Week:</label>
                        <input type="week" id="week" name="userweek">
                    </div>
                    <div>
                        <label for="color">Color:</label>
                        <input type="color" id="color" name="usercolor" value="#e66465">
                    </div>
                </fieldset>

                <fieldset>
                    <legend>Choices</legend>
                    <p>Gender:</p>
                    <input type="radio" id="male" name="gender" value="male">
                    <label for="male">Male</label><br>
                    <input type="radio" id="female" name="gender" value="female">
                    <label for="female">Female</label><br>
                    <input type="radio" id="other" name="gender" value="other">
                    <label for="other">Other</label>

                    <p>Interests:</p>
                    <input type="checkbox" id="coding" name="interest" value="coding">
                    <label for="coding">Coding</label><br>
                    <input type="checkbox" id="music" name="interest" value="music">
                    <label for="music">Music</label><br>
                    <input type="checkbox" id="sports" name="interest" value="sports">
                    <label for="sports">Sports</label>
                </fieldset>

                <fieldset>
                    <legend>Other Inputs</legend>
                    <div>
                        <label for="message">Message:</label>
                        <textarea id="message" name="usermessage" rows="5" cols="30" placeholder="Write your message here..."></textarea>
                    </div>
                    <div>
                        <label for="cars">Choose a car:</label>
                        <select id="cars" name="usercar">
                            <optgroup label="Swedish Cars">
                                <option value="volvo">Volvo</option>
                                <option value="saab">Saab</option>
                            </optgroup>
                            <optgroup label="German Cars">
                                <option value="mercedes">Mercedes</option>
                                <option value="audi" selected>Audi</option>
                            </optgroup>
                        </select>
                    </div>
                    <div>
                        <label for="browser">Choose your browser from the list:</label>
                        <input list="browsers" id="browser" name="userbrowser">
                        <datalist id="browsers">
                            <option value="Edge">
                            <option value="Firefox">
                            <option value="Chrome">
                            <option value="Opera">
                            <option value="Safari">
                        </datalist>
                    </div>
                    <div>
                        <label for="file">Upload a file:</label>
                        <input type="file" id="file" name="userfile" accept="image/png, image/jpeg">
                    </div>
                </fieldset>

                <button type="submit">Submit</button>
                <button type="reset">Reset</button>
                <input type="button" value="Input Button">
                <button type="button" onclick="alert('Button Clicked!')">Click Me Button</button>
            </form>
        </section>

        <section id="table-elements">
            <h2>Table Elements</h2>
            <table>
                <caption>Monthly Savings</caption>
                <colgroup>
                    <col span="1" style="background-color: #f2f2f2;">
                    <col span="2" style="background-color: #e0e0e0;">
                </colgroup>
                <thead>
                    <tr>
                        <th>Month</th>
                        <th>Savings</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>January</td>
                        <td>$100</td>
                        <td rowspan="2">Good start</td>
                    </tr>
                    <tr>
                        <td>February</td>
                        <td>$80</td>
                    </tr>
                    <tr>
                        <td>March</td>
                        <td colspan="2">$120 (Combined with April)</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2">Total Savings</td>
                        <td>$300</td>
                    </tr>
                </tfoot>
            </table>
        </section>

        <section id="semantic-elements">
            <h2>Semantic HTML Elements</h2>
            <article>
                <h3>Article Title</h3>
                <p>This is an article. It represents a self-contained composition in a document, page, application, or site, which is intended to be independently distributable or reusable.</p>
                <details>
                    <summary>More details about the article</summary>
                    <p>This is some additional information hidden by default.</p>
                </details>
            </article>
            <aside>
                <h4>Aside Content</h4>
                <p>This is an aside. It represents a portion of a document whose content is only indirectly related to the document's main content.</p>
            </aside>
            <section>
                <h3>Generic Section</h3>
                <p>This is a generic section, used to group related content.</p>
            </section>
            <time datetime="2024-07-23T10:00:00Z">July 23, 2024, 10:00 AM UTC</time>
            <br>
            <progress value="70" max="100">70%</progress>
            <meter value="2" min="0" max="10" low="3" high="7" optimum="8">2 out of 10</meter>
        </section>

        <section id="flexbox-container" class="flexbox-container-section">
            <h2>Flexbox Example</h2>
            <div class="flex-container">
                <div class="flex-item item1">Item 1</div>
                <div class="flex-item item2">Item 2 (longer content)</div>
                <div class="flex-item item3">Item 3</div>
                <div class="flex-item item4">Item 4</div>
                <div class="flex-item item5">Item 5</div>
            </div>
        </section>

        <section id="grid-container" class="grid-container-section">
            <h2>Grid Example</h2>
            <div class="grid-container">
                <div class="grid-item gitem1">Grid Item 1 (Header)</div>
                <div class="grid-item gitem2">Grid Item 2 (Sidebar)</div>
                <div class="grid-item gitem3">Grid Item 3 (Main Content)</div>
                <div class="grid-item gitem4">Grid Item 4 (Footer)</div>
                <div class="grid-item gitem5">Grid Item 5</div>
                <div class="grid-item gitem6">Grid Item 6</div>
            </div>
        </section>

    </main>

    <footer>
        <p>&copy; 2024 HTML & CSS Guide. All rights reserved.</p>
        <p><a href="#top">Back to top</a></p>
    </footer>

    <script>
        // Basic JavaScript to update range output
        const rangeInput = document.getElementById('range');
        const rangeOutput = document.getElementById('range-output');
        if (rangeInput && rangeOutput) {
            rangeOutput.textContent = rangeInput.value;
            rangeInput.addEventListener('input', () => {
                rangeOutput.textContent = rangeInput.value;
            });
        }
    </script>
</body>
</html>