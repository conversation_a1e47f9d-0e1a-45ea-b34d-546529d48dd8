<?php
// Include required files for database and session management
require_once 'db.php';     // Database connection and operations
require_once 'session.php'; // Session management

/**
 * User Class - Handles all user-related operations
 * 
 * This class provides methods for user registration, login, logout,
 * profile management, and authentication checks.
 */
class User {
    // Private property to store the database instance
    private $db;
    
    /**
     * Constructor - initializes the User object
     * Gets a database instance and initializes the session
     */
    public function __construct() {
        // Get the singleton database instance
        $this->db = Database::getInstance();
        // Initialize the session with security checks
        Session::init();
    }
    
    /**
     * Register a new user
     * 
     * @param string $username The desired username
     * @param string $email The user's email address
     * @param string $password The user's password (will be hashed)
     * @return array Result array with success status and message or user ID
     */
    public function register($username, $email, $password) {
        // Check if username or email already exists in the database
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE username = :username OR email = :email",
            [':username' => $username, ':email' => $email]
        );
        
        // If a user with this username or email exists, return an error
        if ($user) {
            return ['success' => false, 'message' => 'Username or email already exists'];
        }
        
        // Hash the password for secure storage
        // PASSWORD_DEFAULT uses the strongest algorithm currently available (currently bcrypt)
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Prepare data for database insertion
        $data = [
            'username' => $username,
            'email' => $email,
            'password' => $hashedPassword,
            'created_at' => date('Y-m-d H:i:s')  // Current date and time
        ];
        
        try {
            // Insert the new user into the database
            $userId = $this->db->insert('users', $data);
            return ['success' => true, 'user_id' => $userId];
        } catch (PDOException $e) {
            // Return error message if insertion fails
            return ['success' => false, 'message' => 'Registration failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Login a user
     * 
     * @param string $username The username
     * @param string $password The password (plain text)
     * @return array Result array with success status and message or user data
     */
    public function login($username, $password) {
        // Get user by username from the database
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE username = :username",
            [':username' => $username]
        );
        
        // If no user found with this username, return an error
        if (!$user) {
            return ['success' => false, 'message' => 'Invalid username or password'];
        }
        
        // Verify the provided password against the stored hash
        if (password_verify($password, $user['password'])) {
            // Password is correct, set session variables
            Session::set('user_id', $user['id']);
            Session::set('username', $user['username']);
            Session::set('is_logged_in', true);
            
            // Update the last login time in the database
            $this->db->update(
                'users',
                ['last_login' => date('Y-m-d H:i:s')],  // Current date and time
                'id = :id',
                [':id' => $user['id']]
            );
            
            return ['success' => true, 'user' => $user];
        } else {
            // Password is incorrect
            return ['success' => false, 'message' => 'Invalid username or password'];
        }
    }
    
    /**
     * Check if a user is currently logged in
     * 
     * @return bool True if user is logged in, false otherwise
     */
    public function isLoggedIn() {
        return Session::exists('is_logged_in') && Session::get('is_logged_in') === true;
    }
    
    /**
     * Logout the current user
     * 
     * @return bool True on success
     */
    public function logout() {
        // Destroy the session completely
        Session::destroy();
        return true;
    }
    
    /**
     * Get the current logged-in user's data
     * 
     * @return array|null User data or null if not logged in
     */
    public function getCurrentUser() {
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        // Get the user ID from the session
        $userId = Session::get('user_id');
        
        // Fetch user data from the database (excluding the password for security)
        return $this->db->fetchOne(
            "SELECT id, username, email, created_at, last_login FROM users WHERE id = :id",
            [':id' => $userId]
        );
    }
    
    /**
     * Update a user's profile information
     * 
     * @param int $userId The user ID
     * @param array $data The data to update
     * @return array Result array with success status and message
     */
    public function updateProfile($userId, $data) {
        // Don't allow updating of critical fields for security
        unset($data['id']);       // Prevent changing the user ID
        unset($data['password']); // Password should be changed through changePassword method
        
        try {
            // Update the user data in the database
            $this->db->update('users', $data, 'id = :id', [':id' => $userId]);
            return ['success' => true];
        } catch (PDOException $e) {
            // Return error message if update fails
            return ['success' => false, 'message' => 'Update failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Change a user's password
     * 
     * @param int $userId The user ID
     * @param string $currentPassword The current password
     * @param string $newPassword The new password
     * @return array Result array with success status and message
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        // Get the user from the database (including password hash)
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE id = :id",
            [':id' => $userId]
        );
        
        // If no user found with this ID, return an error
        if (!$user) {
            return ['success' => false, 'message' => 'User not found'];
        }
        
        // Verify the current password
        if (!password_verify($currentPassword, $user['password'])) {
            return ['success' => false, 'message' => 'Current password is incorrect'];
        }
        
        // Hash the new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Update the password in the database
        try {
            $this->db->update(
                'users',
                ['password' => $hashedPassword],
                'id = :id',
                [':id' => $userId]
            );
            return ['success' => true];
        } catch (PDOException $e) {
            // Return error message if update fails
            return ['success' => false, 'message' => 'Password change failed: ' . $e->getMessage()];
        }
    }
}
?>