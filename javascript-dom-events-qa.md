# JavaScript DOM & Events Q&A Guide

This comprehensive Q&A guide covers JavaScript DOM manipulation, event handling, browser APIs, and modern JavaScript concepts essential for web development.

## DOM FUNDAMENTALS QUESTIONS

**Q1: What is the DOM and how does it relate to HTML?**
A: The Document Object Model (DOM) is a programming interface for HTML and XML documents:
- **Tree Structure**: Represents HTML as a tree of objects/nodes
- **Live Representation**: Changes to DOM immediately affect the webpage
- **Language Independent**: Can be manipulated by JavaScript, Python, etc.
- **Node Types**: Element nodes, text nodes, attribute nodes, comment nodes
- **API**: Provides methods to access, modify, add, and delete elements

**Q2: What's the difference between `getElementById` and `querySelector`?**
A: Key differences between selection methods:

**getElementById:**
- Returns single element or `null`
- Fastest selection method
- Only searches by ID attribute
- `document.getElementById('myId')`

**querySelector:**
- Returns first matching element or `null`
- Uses CSS selector syntax
- More flexible but slightly slower
- `document.querySelector('#myId')`, `document.querySelector('.class')`

**Performance**: `getElementById` is faster, but `querySelector` is more versatile for complex selections.

**Q3: Explain the difference between `textContent`, `innerHTML`, and `innerText`.**
A: Content manipulation methods comparison:

**textContent:**
- Gets/sets plain text content
- Includes hidden elements
- Safe from XSS attacks
- Faster performance
```javascript
element.textContent = '<script>alert("XSS")</script>'; // Renders as text
```

**innerHTML:**
- Gets/sets HTML content
- Parses and renders HTML tags
- Potential XSS vulnerability
- Slower due to HTML parsing
```javascript
element.innerHTML = '<strong>Bold text</strong>'; // Renders as HTML
```

**innerText:**
- Gets/sets visible text only
- Respects CSS styling (hidden elements excluded)
- Triggers reflow (performance impact)
- Browser-specific behavior differences

**Q4: How do you create and insert elements into the DOM?**
A: Element creation and insertion methods:

**Creating Elements:**
```javascript
// Create element
const div = document.createElement('div');
div.className = 'container';
div.textContent = 'Hello World';

// Create with attributes
const img = document.createElement('img');
img.src = 'image.jpg';
img.alt = 'Description';
```

**Insertion Methods:**
```javascript
// Modern methods (preferred)
parent.append(element);           // Last child
parent.prepend(element);          // First child
element.before(newElement);       // Before element
element.after(newElement);        // After element

// Traditional methods
parent.appendChild(element);      // Last child
parent.insertBefore(element, reference); // Before reference
```

**Performance Tip**: Use `DocumentFragment` for multiple elements:
```javascript
const fragment = document.createDocumentFragment();
for (let i = 0; i < 100; i++) {
    const li = document.createElement('li');
    li.textContent = `Item ${i}`;
    fragment.appendChild(li);
}
list.appendChild(fragment); // Single DOM update
```

## EVENT HANDLING QUESTIONS

**Q5: What's the difference between event capturing and event bubbling?**
A: Event propagation phases:

**Event Capturing (Capture Phase):**
- Events travel from root to target element
- Rarely used in practice
- Enable with `addEventListener(event, handler, true)`

**Event Bubbling (Bubble Phase):**
- Events travel from target to root element
- Default behavior
- Most event handling occurs here

**Example:**
```javascript
// HTML: <div><button>Click</button></div>

// Capture phase
div.addEventListener('click', () => console.log('Div (capture)'), true);

// Bubble phase (default)
button.addEventListener('click', () => console.log('Button'));
div.addEventListener('click', () => console.log('Div (bubble)'));

// Order: Div (capture) → Button → Div (bubble)
```

**Q6: How do you prevent event propagation and default behavior?**
A: Event control methods:

**preventDefault():**
- Prevents default browser behavior
- Form submission, link navigation, etc.
```javascript
form.addEventListener('submit', (e) => {
    e.preventDefault(); // Prevent form submission
    // Handle with JavaScript
});
```

**stopPropagation():**
- Stops event from bubbling/capturing
- Other listeners on same element still execute
```javascript
button.addEventListener('click', (e) => {
    e.stopPropagation(); // Don't bubble to parent
});
```

**stopImmediatePropagation():**
- Stops all further event propagation
- Prevents other listeners on same element
```javascript
button.addEventListener('click', (e) => {
    e.stopImmediatePropagation(); // Stop everything
});
```

**Q7: What is event delegation and why is it useful?**
A: Event delegation uses event bubbling to handle events efficiently:

**Concept:**
- Attach single listener to parent element
- Handle events for multiple child elements
- Works with dynamically added elements

**Benefits:**
- Better performance (fewer event listeners)
- Automatic handling of new elements
- Simplified event management

**Implementation:**
```javascript
// Instead of individual listeners
document.querySelector('.todo-list').addEventListener('click', (e) => {
    const target = e.target;
    
    if (target.matches('.delete-btn')) {
        deleteTodo(target.closest('.todo-item'));
    } else if (target.matches('.edit-btn')) {
        editTodo(target.closest('.todo-item'));
    }
});

// Works for dynamically added todos
function addTodo(text) {
    const todo = document.createElement('div');
    todo.innerHTML = `
        <span>${text}</span>
        <button class="edit-btn">Edit</button>
        <button class="delete-btn">Delete</button>
    `;
    todoList.appendChild(todo); // Events automatically handled
}
```

**Q8: How do you handle keyboard events effectively?**
A: Keyboard event handling best practices:

**Event Types:**
- `keydown`: Key pressed down (repeats if held)
- `keyup`: Key released
- `keypress`: Deprecated, use `keydown`

**Key Properties:**
```javascript
document.addEventListener('keydown', (e) => {
    console.log(e.key);     // 'a', 'Enter', 'ArrowUp'
    console.log(e.code);    // 'KeyA', 'Enter', 'ArrowUp'
    console.log(e.ctrlKey); // true if Ctrl pressed
    console.log(e.shiftKey); // true if Shift pressed
    console.log(e.altKey);  // true if Alt pressed
});
```

**Common Patterns:**
```javascript
// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveDocument();
    }
    
    if (e.key === 'Escape') {
        closeModal();
    }
    
    if (e.key === 'Enter' && e.target.matches('input')) {
        submitForm();
    }
});

// Arrow key navigation
document.addEventListener('keydown', (e) => {
    const focusable = document.querySelectorAll('[tabindex], button, input');
    const current = document.activeElement;
    const currentIndex = Array.from(focusable).indexOf(current);
    
    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            focusable[currentIndex + 1]?.focus();
            break;
        case 'ArrowUp':
            e.preventDefault();
            focusable[currentIndex - 1]?.focus();
            break;
    }
});
```

## ADVANCED DOM QUESTIONS

**Q9: What are the different ways to style elements with JavaScript?**
A: JavaScript styling methods:

**Inline Styles:**
```javascript
element.style.color = 'red';
element.style.backgroundColor = 'yellow';
element.style.fontSize = '16px';

// Multiple styles
Object.assign(element.style, {
    width: '200px',
    height: '100px',
    border: '1px solid black'
});

// CSS custom properties
element.style.setProperty('--main-color', '#3498db');
```

**CSS Classes (Recommended):**
```javascript
// classList API
element.classList.add('active');
element.classList.remove('inactive');
element.classList.toggle('visible');
element.classList.contains('active'); // true/false
element.classList.replace('old', 'new');

// className property
element.className = 'class1 class2';
element.className += ' additional-class';
```

**Computed Styles (Read-only):**
```javascript
const computedStyle = window.getComputedStyle(element);
const color = computedStyle.color;
const fontSize = computedStyle.fontSize;
```

**Q10: How do you work with form data in JavaScript?**
A: Form handling techniques:

**FormData API:**
```javascript
const form = document.querySelector('form');

form.addEventListener('submit', (e) => {
    e.preventDefault();
    
    // Get all form data
    const formData = new FormData(form);
    
    // Access individual fields
    const email = formData.get('email');
    const password = formData.get('password');
    
    // Get all values for multi-select
    const hobbies = formData.getAll('hobbies');
    
    // Convert to object
    const data = Object.fromEntries(formData);
    
    // Send via fetch
    fetch('/api/submit', {
        method: 'POST',
        body: formData // Automatically sets Content-Type
    });
});
```

**Direct Property Access:**
```javascript
// Input values
const email = document.querySelector('#email').value;
const isChecked = document.querySelector('#checkbox').checked;
const selectedOption = document.querySelector('#select').value;

// Radio buttons
const selectedRadio = document.querySelector('input[name="gender"]:checked')?.value;

// Multiple checkboxes
const selectedCheckboxes = Array.from(
    document.querySelectorAll('input[name="hobbies"]:checked')
).map(cb => cb.value);
```

**Form Validation:**
```javascript
function validateForm(form) {
    const errors = {};
    
    // Email validation
    const email = form.email.value;
    if (!email) {
        errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        errors.email = 'Invalid email format';
    }
    
    // Password validation
    const password = form.password.value;
    if (password.length < 8) {
        errors.password = 'Password must be at least 8 characters';
    }
    
    return errors;
}
```

## BROWSER APIS QUESTIONS

**Q11: How do you use localStorage and sessionStorage?**
A: Web Storage API usage:

**localStorage (Persistent):**
```javascript
// Store data (persists until manually cleared)
localStorage.setItem('username', 'john_doe');
localStorage.setItem('settings', JSON.stringify({
    theme: 'dark',
    language: 'en'
}));

// Retrieve data
const username = localStorage.getItem('username');
const settings = JSON.parse(localStorage.getItem('settings') || '{}');

// Remove data
localStorage.removeItem('username');
localStorage.clear(); // Remove all
```

**sessionStorage (Session Only):**
```javascript
// Same API, but data cleared when tab closes
sessionStorage.setItem('tempData', 'value');
const tempData = sessionStorage.getItem('tempData');
```

**Storage Events:**
```javascript
// Listen for storage changes (other tabs)
window.addEventListener('storage', (e) => {
    console.log('Storage changed:', e.key, e.newValue);
    if (e.key === 'theme') {
        updateTheme(e.newValue);
    }
});
```

**Storage Availability Check:**
```javascript
function isStorageAvailable(type) {
    try {
        const storage = window[type];
        const test = '__storage_test__';
        storage.setItem(test, test);
        storage.removeItem(test);
        return true;
    } catch (e) {
        return false;
    }
}
```

**Q12: What is the Fetch API and how do you use it?**
A: Modern AJAX with Fetch API:

**Basic Usage:**
```javascript
// GET request
fetch('https://api.example.com/users')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => console.log(data))
    .catch(error => console.error('Error:', error));

// POST request
fetch('https://api.example.com/users', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123'
    },
    body: JSON.stringify({
        name: 'John Doe',
        email: '<EMAIL>'
    })
})
.then(response => response.json())
.then(data => console.log('Success:', data));
```

**Async/Await Syntax:**
```javascript
async function fetchUsers() {
    try {
        const response = await fetch('https://api.example.com/users');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const users = await response.json();
        return users;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}

// Usage
const users = await fetchUsers();
```

**Error Handling:**
```javascript
async function robustFetch(url, options = {}) {
    try {
        const response = await fetch(url, {
            timeout: 10000, // 10 second timeout
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        if (error instanceof TypeError) {
            throw new Error('Network error - check your connection');
        }
        throw error;
    }
}
```

## PERFORMANCE OPTIMIZATION QUESTIONS

**Q13: How do you optimize DOM manipulation for better performance?**
A: DOM performance optimization strategies:

**Batch DOM Operations:**
```javascript
// Bad: Multiple DOM updates
function updateListSlow(items) {
    const list = document.querySelector('.list');
    items.forEach(item => {
        const li = document.createElement('li');
        li.textContent = item.name;
        list.appendChild(li); // Triggers reflow each time
    });
}

// Good: Single DOM update
function updateListFast(items) {
    const list = document.querySelector('.list');
    const fragment = document.createDocumentFragment();

    items.forEach(item => {
        const li = document.createElement('li');
        li.textContent = item.name;
        fragment.appendChild(li);
    });

    list.appendChild(fragment); // Single reflow
}
```

**Cache DOM References:**
```javascript
// Bad: Repeated queries
function updateElements() {
    document.querySelector('.title').textContent = 'New Title';
    document.querySelector('.title').style.color = 'red';
    document.querySelector('.title').classList.add('active');
}

// Good: Cache reference
function updateElementsOptimized() {
    const title = document.querySelector('.title');
    title.textContent = 'New Title';
    title.style.color = 'red';
    title.classList.add('active');
}
```

**Q14: What are debouncing and throttling, and when should you use them?**
A: Performance techniques for frequent events:

**Debouncing:**
- Delays execution until after calls have stopped
- Use for: search input, resize events, button clicks
```javascript
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Usage: Search as user types
const searchInput = document.querySelector('#search');
const debouncedSearch = debounce(performSearch, 300);
searchInput.addEventListener('input', debouncedSearch);
```

**Throttling:**
- Limits execution to once per time interval
- Use for: scroll events, mouse movement, animation
```javascript
function throttle(func, interval) {
    let lastCall = 0;
    return function(...args) {
        const now = Date.now();
        if (now - lastCall >= interval) {
            lastCall = now;
            func.apply(this, args);
        }
    };
}

// Usage: Scroll event handling
const throttledScroll = throttle(handleScroll, 100);
window.addEventListener('scroll', throttledScroll);
```

**Q15: How do you prevent memory leaks in JavaScript?**
A: Memory leak prevention strategies:

**Remove Event Listeners:**
```javascript
// Bad: Event listener not removed
function createHandler() {
    const largeData = new Array(1000000).fill('data');

    document.addEventListener('click', function() {
        console.log(largeData.length); // Keeps largeData in memory
    });
}

// Good: Clean up listeners
function createHandlerSafe() {
    function clickHandler() {
        console.log('Clicked');
    }

    document.addEventListener('click', clickHandler);

    // Return cleanup function
    return function cleanup() {
        document.removeEventListener('click', clickHandler);
    };
}
```

**Use AbortController:**
```javascript
const controller = new AbortController();
const signal = controller.signal;

// Add multiple listeners with same signal
button.addEventListener('click', handleClick, { signal });
window.addEventListener('resize', handleResize, { signal });

// Remove all listeners at once
controller.abort();
```

## MODERN APIS QUESTIONS

**Q16: What is the Intersection Observer API and how do you use it?**
A: Intersection Observer for efficient visibility detection:

**Lazy Loading Images:**
```javascript
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img); // Stop observing this image
        }
    });
}, {
    threshold: 0.1,        // Trigger when 10% visible
    rootMargin: '50px'     // Expand root margin by 50px
});

document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
});
```

**Infinite Scroll:**
```javascript
const infiniteObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            loadMoreContent();
        }
    });
});

// Observe sentinel element at bottom
const sentinel = document.querySelector('.scroll-sentinel');
infiniteObserver.observe(sentinel);
```

**Q17: How do you create and dispatch custom events?**
A: Custom events for component communication:

**Creating Custom Events:**
```javascript
// Simple custom event
const customEvent = new CustomEvent('userLogin', {
    detail: {
        username: 'john_doe',
        timestamp: Date.now(),
        permissions: ['read', 'write']
    },
    bubbles: true,
    cancelable: true
});

// Dispatch event
document.dispatchEvent(customEvent);

// Listen for custom event
document.addEventListener('userLogin', (event) => {
    console.log('User logged in:', event.detail.username);
    updateUI(event.detail);
});
```

**Event-Driven Architecture:**
```javascript
class EventBus {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Event handler error:', error);
                }
            });
        }
    }

    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }
}

// Usage
const eventBus = new EventBus();
eventBus.on('dataUpdated', (data) => updateChart(data));
eventBus.emit('dataUpdated', { users: [], posts: [] });
```

This comprehensive JavaScript DOM & Events Q&A guide covers essential concepts from basic DOM manipulation to advanced APIs and performance optimization, providing the knowledge needed for modern interactive web development.
