
// ===== BASIC RANGE INPUT FUNCTIONALITY =====
// Get references to the range input element and its output display element
const rangeInput = document.getElementById('range');
const rangeOutput = document.getElementById('range-output');

// Check if both elements exist in the DOM before adding event listeners
if (rangeInput && rangeOutput) {
    // Set the initial text content of the output to match the range input's value
    rangeOutput.textContent = rangeInput.value;
    
    // Add an event listener for the 'input' event (fires whenever the slider is moved)
    rangeInput.addEventListener('input', () => {
        // Update the output text with the current value of the range input
        rangeOutput.textContent = rangeInput.value;
    });
}

// ===== ADVANCED JAVASCRIPT EXAMPLES =====

// DOMContentLoaded event ensures all HTML is loaded before JavaScript runs
// This is crucial for accessing DOM elements safely
document.addEventListener('DOMContentLoaded', () => {
    // ===== DOM MANIPULATION EXAMPLES =====
    
    // Example 1: Modifying existing element content
    // Select the main heading using querySelector (returns the first matching element)
    const mainHeading = document.querySelector('header h1');
    if (mainHeading) {
        // Change the text content of the heading
        mainHeading.textContent = 'HTML, CSS, & JS Comprehensive Guide';
    }

    // Example 2: Creating and appending new elements
    // Select the first section in the main content area
    const firstSection = document.querySelector('main section');
    if (firstSection) {
        // Create a new paragraph element
        const newParagraph = document.createElement('p');
        
        // Set its HTML content (innerHTML allows HTML tags within the string)
        newParagraph.innerHTML = 'This paragraph was dynamically added using <strong>JavaScript</strong>!';
        
        // Apply inline CSS styling
        newParagraph.style.color = 'green';
        
        // Add a CSS class to the element
        newParagraph.classList.add('dynamic-content');
        
        // Append the new paragraph as a child of the first section
        firstSection.appendChild(newParagraph);
    }

    // Example 3: Creating and adding a button element
    // Create a new button element
    const specialButton = document.createElement('button');
    
    // Set its text content
    specialButton.textContent = 'Click for Magic!';
    
    // Assign an ID for easy reference later
    specialButton.id = 'magicButton';
    
    // Find the form section to append the button to
    const formSection = document.getElementById('form-elements');
    if (formSection) {
        // Add the button to the form section
        formSection.appendChild(specialButton);
    }

    // ===== EVENT LISTENING AND HANDLING =====
    
    // Example 1: Click event on a button
    // Get reference to the button we just created
    const magicButton = document.getElementById('magicButton');
    if (magicButton) {
        // Add a click event listener
        magicButton.addEventListener('click', function() {
            // Display an alert dialog
            alert('Magic!');
            
            // Change the button text after clicking
            this.textContent = 'Magic Happened!';
            
            // Disable the button after clicking
            this.disabled = true;
            
            // Note: 'this' refers to the element that triggered the event (magicButton)
        });
    }

    // Example 2: Form input events (focus, blur, input)
    // Get reference to the name input field
    const nameInput = document.getElementById('name');
    if (nameInput) {
        // Focus event - triggers when element receives focus
        nameInput.addEventListener('focus', () => {
            console.log('Name input focused');
            nameInput.style.backgroundColor = 'lightyellow';
        });
        
        // Blur event - triggers when element loses focus
        nameInput.addEventListener('blur', () => {
            console.log('Name input blurred');
            nameInput.style.backgroundColor = '';
        });
        
        // Input event - triggers whenever the input value changes
        nameInput.addEventListener('input', (event) => {
            // event.target refers to the element that triggered the event
            console.log(`Name input value changed: ${event.target.value}`);
        });
    }

    // ===== EVENT PROPAGATION (BUBBLING AND CAPTURING) =====
    // This demonstrates how events travel through the DOM tree
    
    // Get references to the flex container and all flex items
    const flexContainer = document.querySelector('.flex-container');
    const flexItems = document.querySelectorAll('.flex-item');

    if (flexContainer) {
        // Capturing phase listener on container
        // The third parameter 'true' specifies capturing phase (top-down)
        flexContainer.addEventListener('click', (event) => {
            console.log('Flex Container Clicked (Capturing)');
            // Uncomment the next line to stop event propagation at container during capture
            // event.stopPropagation(); 
        }, true); 

        // Bubbling phase listener on container (default behavior)
        // Events bubble up from the target element to ancestors
        flexContainer.addEventListener('click', (event) => {
            console.log('Flex Container Clicked (Bubbling)');
            
            // event.target is the actual element that was clicked
            console.log(`Event target: ${event.target.textContent}`);
            
            // event.currentTarget is the element the listener is attached to (container)
            console.log(`Event currentTarget: ${event.currentTarget.className}`);
        });
    }

    // Add click event listeners to each flex item (bubbling phase)
    flexItems.forEach(item => {
        item.addEventListener('click', (event) => {
            console.log(`${item.textContent} Clicked (Bubbling)`);
            
            // Uncomment to prevent the event from bubbling up to parent elements
            // event.stopPropagation(); 
        });
    });

    // ===== EVENT DELEGATION EXAMPLE =====
    // Instead of adding event listeners to each child element,
    // add one listener to a parent and determine which child was clicked
    
    const listElementsSection = document.getElementById('list-elements');
    if (listElementsSection) {
        const unorderedList = listElementsSection.querySelector('ul');
        if (unorderedList) {
            // Single event listener on the parent <ul> element
            unorderedList.addEventListener('click', function(event) {
                // Check if the clicked element is a list item
                if (event.target.tagName === 'LI') {
                    console.log(`Delegated click: You clicked on list item: ${event.target.firstChild.textContent.trim()}`);
                    
                    // Toggle line-through style on the clicked item
                    event.target.style.textDecoration = 
                        event.target.style.textDecoration === 'line-through' ? '' : 'line-through';
                }
            });
        }
    }

    // ===== CUSTOM EVENTS EXAMPLE =====
    // Create a button to trigger a custom event
    const customEventButton = document.createElement('button');
    customEventButton.textContent = 'Trigger Custom Event';
    customEventButton.id = 'customEventTrigger';
    if (firstSection) {
        firstSection.appendChild(customEventButton);
    }

    // Add a listener for the custom event
    document.addEventListener('myCustomEvent', function(event) {
        console.log('Custom event "myCustomEvent" caught!');
        console.log('Detail:', event.detail); // Access custom data passed with the event
        alert(`Custom Event Triggered: ${event.detail.message}`);
    });

    if (customEventButton) {
        // Add click listener to the button
        customEventButton.addEventListener('click', () => {
            // Create a new custom event with data
            const myEvent = new CustomEvent('myCustomEvent', {
                detail: { // Custom data to pass with the event
                    message: 'Hello from the custom event!',
                    timestamp: new Date().toLocaleTimeString()
                },
                bubbles: true,    // Whether the event bubbles up through the DOM
                cancelable: true  // Whether the event can be canceled
            });
            
            // Dispatch (trigger) the custom event
            customEventButton.dispatchEvent(myEvent);
        });
    }

    // ===== ADVANCED FORM HANDLING EXAMPLE =====
    const mainForm = document.querySelector('#form-elements form');
    if (mainForm) {
        // Add submit event listener to the form
        mainForm.addEventListener('submit', function(event) {
            // Prevent the default form submission behavior
            event.preventDefault(); 
            console.log('Form submission intercepted by JavaScript.');

            // Create a FormData object from the form
            // FormData automatically collects all form field values
            const formData = new FormData(this);
            
            // Process and display the form data
            let formOutput = 'Form Data:\n';
            for (const [key, value] of formData.entries()) {
                formOutput += `${key}: ${value}\n`;
            }
            console.log(formOutput);
            alert('Form submitted! Check the console for data. (Actual submission prevented)');
            
            // In a real application, you would typically send the data to a server
            // Example: fetch('/submit-form', { method: 'POST', body: formData });
        });
    }

    // ===== JAVASCRIPT MEDIA QUERIES EXAMPLE =====
    // Function to handle changes in media query state
    function handleMediaQueryChange(mediaQuery) {
        if (mediaQuery.matches) { // If media query matches (screen width <= 768px)
            console.log('Media query matched: Small screen detected');
            
            // Add a notification for small screens
            const mobileNotice = document.createElement('div');
            mobileNotice.id = 'mobile-notice';
            mobileNotice.style.backgroundColor = '#ffeb3b';
            mobileNotice.style.color = '#333';
            mobileNotice.style.padding = '10px';
            mobileNotice.style.textAlign = 'center';
            mobileNotice.style.position = 'fixed';
            mobileNotice.style.bottom = '0';
            mobileNotice.style.left = '0';
            mobileNotice.style.right = '0';
            mobileNotice.style.zIndex = '1000';
            mobileNotice.style.boxShadow = '0 -2px 5px rgba(0,0,0,0.2)';
            mobileNotice.textContent = 'You are viewing in mobile mode';
            
            // Only add if it doesn't already exist
            if (!document.getElementById('mobile-notice')) {
                document.body.appendChild(mobileNotice);
            }
            
            // Simplify the navigation for mobile
            const navItems = document.querySelectorAll('header nav ul li');
            navItems.forEach(item => {
                item.style.display = 'block';
                item.style.margin = '10px 0';
            });
            
            // Make buttons larger for touch screens
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            allButtons.forEach(button => {
                button.style.padding = '15px 20px';
                button.style.fontSize = '1.1em';
            });
            
        } else { // If media query doesn't match (screen width > 768px)
            console.log('Media query not matched: Large screen detected');
            
            // Remove the mobile notification if it exists
            const mobileNotice = document.getElementById('mobile-notice');
            if (mobileNotice) {
                mobileNotice.remove();
            }
            
            // Reset navigation styling
            const navItems = document.querySelectorAll('header nav ul li');
            navItems.forEach(item => {
                item.style.display = '';
                item.style.margin = '';
            });
            
            // Reset button sizing
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            allButtons.forEach(button => {
                button.style.padding = '';
                button.style.fontSize = '';
            });
        }
    }
    
    // Create a media query list and add a listener
    // This detects when the screen width is 768px or less
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    
    // Initial check when the page loads
    handleMediaQueryChange(mediaQuery);
    
    // Add listener for changes in screen size
    // Using try/catch for browser compatibility
    try {
        // Modern browsers use addEventListener
        mediaQuery.addEventListener('change', handleMediaQueryChange);
    } catch (e) {
        // Fallback for older browsers that use addListener
        mediaQuery.addListener(handleMediaQueryChange);
    }
    
    // Additional media query for very small screens (mobile phones)
    const smallScreenQuery = window.matchMedia('(max-width: 480px)');
    
    // Function to handle very small screen changes
    function handleSmallScreenChange(mediaQuery) {
        if (mediaQuery.matches) { // Screen width <= 480px
            console.log('Very small screen detected');
            
            // Add a class to the body for potential CSS targeting
            document.body.classList.add('very-small-screen');
            
            // Increase font size for better readability on small screens
            document.body.style.fontSize = '16px';
            
            // Stack form elements vertically with more spacing
            const formDivs = document.querySelectorAll('#form-elements form div');
            formDivs.forEach(div => {
                div.style.marginBottom = '20px';
            });
            
        } else { // Screen width > 480px
            console.log('Not a very small screen');
            
            // Remove small screen specific changes
            document.body.classList.remove('very-small-screen');
            document.body.style.fontSize = '';
            
            // Reset form spacing
            const formDivs = document.querySelectorAll('#form-elements form div');
            formDivs.forEach(div => {
                div.style.marginBottom = '';
            });
        }
    }
    
    // Initial check for small screens
    handleSmallScreenChange(smallScreenQuery);
    
    // Add listener for small screen changes
    try {
        smallScreenQuery.addEventListener('change', handleSmallScreenChange);
    } catch (e) {
        smallScreenQuery.addListener(handleSmallScreenChange);
    }
    
    // ===== TESTING UTILITY =====
    // Create a button to toggle mobile view for testing purposes
    const mobileTestButton = document.createElement('button');
    mobileTestButton.textContent = 'Toggle Mobile View (For Testing)';
    mobileTestButton.id = 'mobile-test-button';
    mobileTestButton.style.position = 'fixed';
    mobileTestButton.style.top = '10px';
    mobileTestButton.style.right = '10px';
    mobileTestButton.style.zIndex = '1001';
    mobileTestButton.style.backgroundColor = '#4CAF50';
    mobileTestButton.style.color = 'white';
    mobileTestButton.style.border = 'none';
    mobileTestButton.style.borderRadius = '4px';
    mobileTestButton.style.padding = '8px 12px';
    mobileTestButton.style.cursor = 'pointer';
    
    // Add the test button to the document
    document.body.appendChild(mobileTestButton);
    
    // Track if mobile view is being forced
    let mobileViewForced = false;
    
    // Add click event listener to toggle mobile view
    mobileTestButton.addEventListener('click', () => {
        // Toggle the state
        mobileViewForced = !mobileViewForced;
        
        if (mobileViewForced) {
            // Force mobile view by manually triggering the handlers
            handleMediaQueryChange({ matches: true });
            handleSmallScreenChange({ matches: window.innerWidth <= 480 });
            
            // Update button appearance
            mobileTestButton.textContent = 'Exit Mobile View Test';
            mobileTestButton.style.backgroundColor = '#f44336';
        } else {
            // Return to actual screen size detection
            handleMediaQueryChange({ matches: window.innerWidth <= 768 });
            handleSmallScreenChange({ matches: window.innerWidth <= 480 });
            
            // Reset button appearance
            mobileTestButton.textContent = 'Toggle Mobile View (For Testing)';
            mobileTestButton.style.backgroundColor = '#4CAF50';
        }
    });

    // Log that all JavaScript has been loaded and executed
    console.log('Advanced JavaScript examples loaded and executed.');
});