# PHP Comprehensive Reference Guide

This document provides a complete reference of PHP syntax, functions, concepts, and features essential for web development.

## PHP BASICS

### PHP Tags and Syntax
- **`<?php ?>`** - Standard PHP opening and closing tags
- **`<?= ?>`** - Short echo tag (equivalent to `<?php echo ?>`)
- **`;`** - Statement terminator (required for most statements)
- **`//`** - Single-line comment
- **`/* */`** - Multi-line comment
- **`#`** - Alternative single-line comment

### Variables and Data Types
- **`$variable`** - Variable declaration (starts with $)
- **String** - Text data: `"Hello"` or `'Hello'`
- **Integer** - Whole numbers: `42`, `-10`
- **Float/Double** - Decimal numbers: `3.14`, `2.5`
- **<PERSON>olean** - True/false values: `true`, `false`
- **Array** - Collection of values: `array()` or `[]`
- **Object** - Instance of a class
- **NULL** - Represents no value
- **Resource** - External resource reference

### Operators
- **Arithmetic**: `+`, `-`, `*`, `/`, `%` (modulo), `**` (exponentiation)
- **Assignment**: `=`, `+=`, `-=`, `*=`, `/=`, `.=` (concatenation)
- **Comparison**: `==`, `===`, `!=`, `!==`, `<`, `>`, `<=`, `>=`
- **Logical**: `&&` (and), `||` (or), `!` (not)
- **String**: `.` (concatenation)
- **Increment/Decrement**: `++`, `--`
- **Ternary**: `condition ? true_value : false_value`
- **Null Coalescing**: `??` (PHP 7+)

## CONTROL STRUCTURES

### Conditional Statements
- **`if`** - Execute code if condition is true
- **`else`** - Execute code if condition is false
- **`elseif`** - Multiple conditions
- **`switch`** - Multiple value comparison
- **`match`** - Modern switch alternative (PHP 8+)

### Loops
- **`for`** - Loop with counter
- **`while`** - Loop while condition is true
- **`do-while`** - Execute at least once, then loop
- **`foreach`** - Loop through arrays/objects
- **`break`** - Exit loop
- **`continue`** - Skip current iteration

## FUNCTIONS

### Built-in String Functions
- **`strlen()`** - String length
- **`substr()`** - Extract substring
- **`strpos()`** - Find position of substring
- **`str_replace()`** - Replace text in string
- **`trim()`** - Remove whitespace
- **`strtolower()`** - Convert to lowercase
- **`strtoupper()`** - Convert to uppercase
- **`explode()`** - Split string into array
- **`implode()`** - Join array into string
- **`htmlspecialchars()`** - Convert special characters to HTML entities
- **`strip_tags()`** - Remove HTML tags
- **`addslashes()`** - Add backslashes before quotes
- **`stripslashes()`** - Remove backslashes

### Array Functions
- **`count()`** - Count array elements
- **`array_push()`** - Add elements to end
- **`array_pop()`** - Remove last element
- **`array_shift()`** - Remove first element
- **`array_unshift()`** - Add elements to beginning
- **`array_merge()`** - Merge arrays
- **`array_keys()`** - Get array keys
- **`array_values()`** - Get array values
- **`in_array()`** - Check if value exists
- **`array_search()`** - Search for value
- **`sort()`** - Sort array
- **`rsort()`** - Reverse sort
- **`asort()`** - Sort associative array by value
- **`ksort()`** - Sort associative array by key

### Date and Time Functions
- **`date()`** - Format timestamp
- **`time()`** - Current Unix timestamp
- **`mktime()`** - Create timestamp
- **`strtotime()`** - Parse date string
- **`DateTime`** - Object-oriented date handling

### Math Functions
- **`abs()`** - Absolute value
- **`round()`** - Round number
- **`ceil()`** - Round up
- **`floor()`** - Round down
- **`max()`** - Maximum value
- **`min()`** - Minimum value
- **`rand()`** - Random number
- **`mt_rand()`** - Better random number

### File System Functions
- **`fopen()`** - Open file
- **`fclose()`** - Close file
- **`fread()`** - Read file
- **`fwrite()`** - Write to file
- **`file_get_contents()`** - Read entire file
- **`file_put_contents()`** - Write entire file
- **`file_exists()`** - Check if file exists
- **`is_file()`** - Check if it's a file
- **`is_dir()`** - Check if it's a directory
- **`unlink()`** - Delete file
- **`mkdir()`** - Create directory

## SUPERGLOBALS

### Global Arrays
- **`$_GET`** - URL parameters
- **`$_POST`** - Form data (POST method)
- **`$_REQUEST`** - Combined GET, POST, COOKIE data
- **`$_SESSION`** - Session variables
- **`$_COOKIE`** - Cookie values
- **`$_FILES`** - Uploaded file information
- **`$_SERVER`** - Server and environment information
- **`$_ENV`** - Environment variables
- **`$GLOBALS`** - Global scope variables

### Common $_SERVER Variables
- **`$_SERVER['REQUEST_METHOD']`** - HTTP method (GET, POST, etc.)
- **`$_SERVER['REQUEST_URI']`** - Requested URI
- **`$_SERVER['HTTP_HOST']`** - Host header
- **`$_SERVER['SERVER_NAME']`** - Server name
- **`$_SERVER['DOCUMENT_ROOT']`** - Document root directory
- **`$_SERVER['SCRIPT_NAME']`** - Current script path
- **`$_SERVER['QUERY_STRING']`** - Query string
- **`$_SERVER['HTTP_USER_AGENT']`** - User agent string
- **`$_SERVER['REMOTE_ADDR']`** - Client IP address

## OBJECT-ORIENTED PROGRAMMING

### Class Concepts
- **`class`** - Define a class
- **`new`** - Create object instance
- **`$this`** - Reference to current object
- **`public`** - Accessible everywhere
- **`private`** - Accessible only within same class
- **`protected`** - Accessible within class and subclasses
- **`static`** - Belongs to class, not instance
- **`const`** - Class constant
- **`extends`** - Inheritance
- **`implements`** - Interface implementation
- **`abstract`** - Abstract class/method
- **`final`** - Cannot be overridden
- **`interface`** - Contract definition
- **`trait`** - Code reuse mechanism

### Magic Methods
- **`__construct()`** - Constructor
- **`__destruct()`** - Destructor
- **`__get()`** - Property getter
- **`__set()`** - Property setter
- **`__toString()`** - String representation
- **`__call()`** - Method overloading
- **`__autoload()`** - Automatic class loading

## DATABASE INTERACTION

### MySQLi Functions
- **`mysqli_connect()`** - Connect to database
- **`mysqli_select_db()`** - Select database
- **`mysqli_query()`** - Execute query
- **`mysqli_fetch_array()`** - Fetch result as array
- **`mysqli_fetch_assoc()`** - Fetch associative array
- **`mysqli_num_rows()`** - Number of rows
- **`mysqli_affected_rows()`** - Affected rows count
- **`mysqli_close()`** - Close connection
- **`mysqli_real_escape_string()`** - Escape special characters

### PDO (PHP Data Objects)
- **`PDO::__construct()`** - Create PDO instance
- **`prepare()`** - Prepare statement
- **`execute()`** - Execute prepared statement
- **`fetch()`** - Fetch single row
- **`fetchAll()`** - Fetch all rows
- **`bindParam()`** - Bind parameter
- **`bindValue()`** - Bind value
- **`rowCount()`** - Row count

## ERROR HANDLING

### Error Functions
- **`error_reporting()`** - Set error reporting level
- **`ini_set()`** - Set configuration option
- **`trigger_error()`** - Generate user error
- **`set_error_handler()`** - Set custom error handler
- **`try...catch`** - Exception handling
- **`throw`** - Throw exception
- **`finally`** - Always executed block

### Common Error Types
- **E_ERROR** - Fatal errors
- **E_WARNING** - Warning errors
- **E_NOTICE** - Notice errors
- **E_PARSE** - Parse errors
- **E_ALL** - All errors

## SESSION AND COOKIE MANAGEMENT

### Session Functions
- **`session_start()`** - Start session
- **`session_destroy()`** - Destroy session
- **`session_unset()`** - Unset session variables
- **`session_regenerate_id()`** - Regenerate session ID
- **`session_name()`** - Get/set session name
- **`session_id()`** - Get/set session ID

### Cookie Functions
- **`setcookie()`** - Set cookie
- **`setrawcookie()`** - Set cookie without URL encoding
- **Cookie parameters**: name, value, expire, path, domain, secure, httponly

## FORM HANDLING AND VALIDATION

### Input Validation
- **`filter_var()`** - Filter variable with specified filter
- **`filter_input()`** - Get filtered input
- **`FILTER_VALIDATE_EMAIL`** - Email validation
- **`FILTER_VALIDATE_URL`** - URL validation
- **`FILTER_VALIDATE_INT`** - Integer validation
- **`FILTER_SANITIZE_STRING`** - String sanitization
- **`FILTER_SANITIZE_EMAIL`** - Email sanitization

### Security Functions
- **`password_hash()`** - Hash password
- **`password_verify()`** - Verify password
- **`hash()`** - Generate hash
- **`md5()`** - MD5 hash (not recommended for passwords)
- **`sha1()`** - SHA1 hash (not recommended for passwords)
- **`crypt()`** - One-way string hashing

## FILE UPLOAD HANDLING

### Upload Functions
- **`move_uploaded_file()`** - Move uploaded file
- **`is_uploaded_file()`** - Check if file was uploaded
- **`$_FILES` array structure**:
  - `name` - Original filename
  - `type` - MIME type
  - `size` - File size in bytes
  - `tmp_name` - Temporary file path
  - `error` - Upload error code

## REGULAR EXPRESSIONS

### PCRE Functions
- **`preg_match()`** - Perform regular expression match
- **`preg_match_all()`** - Perform global regular expression match
- **`preg_replace()`** - Perform regular expression search and replace
- **`preg_split()`** - Split string by regular expression

## INCLUDE AND REQUIRE

### File Inclusion
- **`include`** - Include file (warning if not found)
- **`include_once`** - Include file only once
- **`require`** - Require file (fatal error if not found)
- **`require_once`** - Require file only once

## NAMESPACES AND AUTOLOADING

### Namespace Features
- **`namespace`** - Declare namespace
- **`use`** - Import namespace/class
- **`\`** - Namespace separator
- **`__NAMESPACE__`** - Current namespace constant

### Autoloading
- **`spl_autoload_register()`** - Register autoload function
- **Composer autoloading** - PSR-4 standard

## DETAILED EXPLANATIONS AND EXAMPLES

### PHP Configuration
- **`php.ini`** - Main configuration file
- **`phpinfo()`** - Display PHP configuration
- **`ini_get()`** - Get configuration value
- **`ini_set()`** - Set configuration value
- **`get_loaded_extensions()`** - List loaded extensions
- **`extension_loaded()`** - Check if extension is loaded

### Variable Handling Functions
- **`isset()`** - Check if variable is set
- **`empty()`** - Check if variable is empty
- **`unset()`** - Destroy variable
- **`var_dump()`** - Display variable information
- **`print_r()`** - Print human-readable information
- **`gettype()`** - Get variable type
- **`settype()`** - Set variable type
- **`is_array()`** - Check if variable is array
- **`is_string()`** - Check if variable is string
- **`is_numeric()`** - Check if variable is numeric
- **`is_null()`** - Check if variable is null

### Advanced Array Operations
- **Associative Arrays**: `$array['key'] = 'value'`
- **Multidimensional Arrays**: `$array[0][1] = 'value'`
- **`array_map()`** - Apply callback to array elements
- **`array_filter()`** - Filter array elements
- **`array_reduce()`** - Reduce array to single value
- **`array_walk()`** - Apply function to array elements
- **`array_slice()`** - Extract portion of array
- **`array_splice()`** - Remove/replace array elements
- **`array_unique()`** - Remove duplicate values
- **`array_flip()`** - Exchange keys and values

### String Manipulation Advanced
- **`sprintf()`** - Format string
- **`printf()`** - Output formatted string
- **`str_pad()`** - Pad string to length
- **`str_repeat()`** - Repeat string
- **`str_shuffle()`** - Randomly shuffle string
- **`wordwrap()`** - Wrap string to given length
- **`nl2br()`** - Convert newlines to HTML breaks
- **`parse_str()`** - Parse query string into variables
- **`http_build_query()`** - Build query string

### JSON Handling
- **`json_encode()`** - Encode to JSON
- **`json_decode()`** - Decode from JSON
- **`JSON_PRETTY_PRINT`** - Pretty print JSON
- **`JSON_UNESCAPED_UNICODE`** - Don't escape Unicode
- **`json_last_error()`** - Get last JSON error

### URL and HTTP Functions
- **`urlencode()`** - URL encode string
- **`urldecode()`** - URL decode string
- **`rawurlencode()`** - Raw URL encode
- **`rawurldecode()`** - Raw URL decode
- **`parse_url()`** - Parse URL components
- **`http_response_code()`** - Set/get HTTP response code
- **`header()`** - Send HTTP header
- **`headers_sent()`** - Check if headers sent

### Cryptography and Hashing
- **`openssl_encrypt()`** - Encrypt data
- **`openssl_decrypt()`** - Decrypt data
- **`random_bytes()`** - Generate random bytes
- **`bin2hex()`** - Convert binary to hex
- **`hex2bin()`** - Convert hex to binary
- **`base64_encode()`** - Base64 encode
- **`base64_decode()`** - Base64 decode

### Modern PHP Features (PHP 7+)

#### Type Declarations
- **Scalar types**: `int`, `float`, `string`, `bool`
- **Return types**: `function name(): int`
- **Nullable types**: `?string` (PHP 7.1+)
- **Union types**: `int|string` (PHP 8+)
- **Mixed type**: `mixed` (PHP 8+)

#### New Operators
- **Spaceship operator**: `<=>` (three-way comparison)
- **Null coalescing**: `??`
- **Null coalescing assignment**: `??=` (PHP 7.4+)
- **Arrow functions**: `fn($x) => $x * 2` (PHP 7.4+)

#### Advanced OOP Features
- **Anonymous classes**: `new class { }`
- **Traits**: Code reuse without inheritance
- **Interfaces**: Contract definitions
- **Abstract classes**: Partial implementations
- **Method chaining**: Return `$this` for fluent interface

### Error Handling Best Practices
- **Custom exceptions**: Extend `Exception` class
- **Exception hierarchy**: Specific exception types
- **Logging**: `error_log()` function
- **Debug information**: `debug_backtrace()`
- **Production vs development**: Different error handling

### Security Best Practices
- **Input validation**: Always validate user input
- **SQL injection prevention**: Use prepared statements
- **XSS prevention**: Use `htmlspecialchars()`
- **CSRF protection**: Use tokens
- **Password security**: Use `password_hash()` and `password_verify()`
- **File upload security**: Validate file types and sizes
- **Session security**: Regenerate session IDs

### Performance Optimization
- **Opcode caching**: OPcache extension
- **Memory management**: `memory_get_usage()`
- **Profiling**: Xdebug profiler
- **Database optimization**: Proper indexing, query optimization
- **Caching strategies**: File, memory, database caching

### Common Design Patterns
- **Singleton**: Single instance pattern
- **Factory**: Object creation pattern
- **Observer**: Event handling pattern
- **MVC**: Model-View-Controller architecture
- **Repository**: Data access pattern
- **Dependency Injection**: Loose coupling pattern

### Framework Concepts
- **Routing**: URL to controller mapping
- **Middleware**: Request/response filtering
- **ORM**: Object-Relational Mapping
- **Templating**: View layer separation
- **Dependency Container**: Service management
- **Event System**: Decoupled communication

### Testing
- **PHPUnit**: Unit testing framework
- **Assertions**: Test conditions
- **Mocking**: Fake objects for testing
- **Code coverage**: Test completeness measurement
- **Integration testing**: Component interaction testing

### Package Management
- **Composer**: Dependency manager
- **Packagist**: Package repository
- **PSR standards**: PHP Standards Recommendations
- **Autoloading**: Automatic class loading
- **Semantic versioning**: Version numbering

### Web Development Concepts
- **RESTful APIs**: HTTP method conventions
- **Authentication**: User verification
- **Authorization**: Permission checking
- **Rate limiting**: Request throttling
- **CORS**: Cross-origin resource sharing
- **WebSockets**: Real-time communication

This comprehensive PHP reference covers fundamental concepts through advanced features, providing a complete resource for PHP web development from beginner to expert level.
