<?php
// Include the configuration file that contains database connection parameters
require_once 'config.php';

/**
 * Database Class - Handles all database operations using PDO
 * 
 * This class implements the Singleton pattern to ensure only one database connection
 * exists throughout the application, which improves performance and resource usage.
 */
class Database {
    // Private property to store the PDO connection object
    private $pdo;
    
    // Static property to store the single instance of this class
    private static $instance = null;
    
    /**
     * Private constructor - implements the Singleton pattern
     * This prevents creating multiple instances with 'new Database()'
     */
    private function __construct() {
        // Create the DSN (Data Source Name) string from config constants
        // The DSN contains all the information needed to connect to the database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        
        // Set PDO options for better performance and error handling
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,    // Throw exceptions on errors instead of silent failures
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,           // Return results as associative arrays by default
            PDO::ATTR_EMULATE_PREPARES   => false,                      // Use real prepared statements for better security
        ];
        
        try {
            // Create a new PDO instance (database connection)
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            // Re-throw the exception with the same message and code
            throw new PDOException($e->getMessage(), (int)$e->getCode());
        }
    }
    
    /**
     * Get the singleton instance of the Database class
     * This is the public method used to access the database connection
     * 
     * @return Database The single instance of the Database class
     */
    public static function getInstance() {
        // Create a new instance only if one doesn't already exist
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get the PDO connection object
     * 
     * @return PDO The PDO connection object
     */
    public function getConnection() {
        return $this->pdo;
    }
    
    /**
     * Execute a SQL query with parameters
     * 
     * @param string $sql The SQL query with placeholders
     * @param array $params The parameters to bind to the query
     * @return PDOStatement The prepared and executed statement
     */
    public function query($sql, $params = []) {
        // Prepare the SQL statement (creates a PDOStatement object)
        $stmt = $this->pdo->prepare($sql);
        // Execute the statement with the provided parameters
        $stmt->execute($params);
        return $stmt;
    }
    
    /**
     * Fetch a single row from the database
     * 
     * @param string $sql The SQL query with placeholders
     * @param array $params The parameters to bind to the query
     * @return array|false The fetched row as an associative array, or false if no row found
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();  // Returns a single row
    }
    
    /**
     * Fetch all rows from the database
     * 
     * @param string $sql The SQL query with placeholders
     * @param array $params The parameters to bind to the query
     * @return array The fetched rows as an array of associative arrays
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();  // Returns all rows
    }
    
    /**
     * Insert a record into a table
     * 
     * @param string $table The name of the table
     * @param array $data Associative array of column names and values
     * @return string The ID of the inserted record
     */
    public function insert($table, $data) {
        // Extract the column names from the data array
        $keys = array_keys($data);
        // Create the field list with backticks for SQL syntax
        $fields = '`' . implode('`, `', $keys) . '`';
        // Create the placeholder list for prepared statement
        $placeholders = ':' . implode(', :', $keys);
        
        // Build the SQL query
        $sql = "INSERT INTO `{$table}` ({$fields}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        // Return the ID of the last inserted row
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Update records in a table
     * 
     * @param string $table The name of the table
     * @param array $data Associative array of column names and values to update
     * @param string $where The WHERE clause of the SQL statement
     * @param array $whereParams Parameters for the WHERE clause
     * @return int The number of rows affected
     */
    public function update($table, $data, $where, $whereParams = []) {
        // Build the SET clause for the SQL query
        $set = [];
        foreach ($data as $key => $value) {
            $set[] = "`{$key}` = :{$key}";
        }
        
        // Build the complete SQL query
        $sql = "UPDATE `{$table}` SET " . implode(', ', $set) . " WHERE {$where}";
        
        // Merge data and where parameters for the prepared statement
        $params = array_merge($data, $whereParams);
        
        // Execute the query and return the number of affected rows
        return $this->query($sql, $params)->rowCount();
    }
    
    /**
     * Delete records from a table
     * 
     * @param string $table The name of the table
     * @param string $where The WHERE clause of the SQL statement
     * @param array $params Parameters for the WHERE clause
     * @return int The number of rows affected
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM `{$table}` WHERE {$where}";
        return $this->query($sql, $params)->rowCount();
    }
    
    /**
     * Begin a database transaction
     * Transactions allow multiple operations to be treated as a single unit
     * 
     * @return bool True on success
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * Commit a database transaction
     * This makes all changes permanent that were made since beginTransaction()
     * 
     * @return bool True on success
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * Rollback a database transaction
     * This cancels all changes that were made since beginTransaction()
     * 
     * @return bool True on success
     */
    public function rollback() {
        return $this->pdo->rollBack();
    }
}
?>