
/*
===========================================
JAVASCRIPT COMPREHENSIVE GUIDE
===========================================
This JavaScript file demonstrates modern JavaScript concepts, DOM manipulation,
event handling, and interactive web development techniques.

Organization:
1. Basic DOM Interaction
2. Advanced DOM Manipulation
3. Event Handling and Propagation
4. Form Processing
5. Responsive JavaScript
6. Modern JavaScript Features
7. Testing and Debugging Utilities
===========================================
*/

/*
===========================================
BASIC RANGE INPUT FUNCTIONALITY
===========================================
Demonstrates fundamental DOM element selection and event handling
This is a practical example of JavaScript enhancing HTML form controls
*/

/*
DOM Element Selection
document.getElementById() is the most direct way to select elements by their ID
Returns null if no element with the specified ID exists
*/
const rangeInput = document.getElementById('range');
const rangeOutput = document.getElementById('range-output');

/*
Defensive Programming with Null Checks
Always check if elements exist before manipulating them
Prevents runtime errors if HTML structure changes
*/
if (rangeInput && rangeOutput) {
    /*
    Initial Value Synchronization
    textContent property sets or gets the text content of an element
    Safer than innerHTML when dealing with plain text
    */
    rangeOutput.textContent = rangeInput.value;

    /*
    Event Listener Registration
    addEventListener() is the modern way to attach event handlers
    'input' event fires continuously as the user drags the slider
    Arrow function provides concise syntax for simple callbacks
    */
    rangeInput.addEventListener('input', () => {
        /*
        Real-time Value Update
        This creates immediate visual feedback as the user interacts
        Essential for good user experience in interactive elements
        */
        rangeOutput.textContent = rangeInput.value;
    });
}

/*
===========================================
DOCUMENT READY STATE HANDLING
===========================================
Ensures JavaScript runs only after the DOM is fully constructed
Critical for reliable DOM manipulation
*/

/*
DOMContentLoaded Event
Fires when the HTML document has been completely loaded and parsed
Safer than window.onload which waits for all resources (images, etc.)
Best practice for DOM manipulation scripts
*/
document.addEventListener('DOMContentLoaded', () => {
    /*
    ===========================================
    ADVANCED DOM MANIPULATION EXAMPLES
    ===========================================
    Demonstrates various ways to select, modify, and create DOM elements
    */

    /*
    Example 1: Modifying Existing Element Content
    querySelector() uses CSS selector syntax to find elements
    Returns the first matching element or null if none found
    More flexible than getElementById() but slightly slower
    */
    const mainHeading = document.querySelector('header h1');

    /*
    Null Check and Content Modification
    Always verify element exists before manipulation
    textContent is safer than innerHTML for plain text
    */
    if (mainHeading) {
        /*
        Dynamic Content Update
        Changes the page title to reflect JavaScript integration
        Demonstrates how JavaScript can enhance static HTML content
        */
        mainHeading.textContent = 'HTML, CSS, & JS Comprehensive Guide';
    }

    /*
    Example 2: Creating and Appending New Elements
    Demonstrates dynamic DOM element creation and insertion
    Essential skill for building interactive web applications
    */

    /*
    Element Selection for Parent Container
    querySelector() with descendant selector finds first section inside main
    This provides a target location for our new content
    */
    const firstSection = document.querySelector('main section');

    if (firstSection) {
        /*
        Dynamic Element Creation
        document.createElement() creates a new HTML element in memory
        Element is not yet part of the DOM until explicitly added
        */
        const newParagraph = document.createElement('p');

        /*
        Setting HTML Content with innerHTML
        innerHTML allows insertion of HTML markup within the element
        More powerful than textContent but requires caution with user input
        Can be a security risk if used with untrusted content (XSS attacks)
        */
        newParagraph.innerHTML = 'This paragraph was dynamically added using <strong>JavaScript</strong>!';

        /*
        Inline CSS Styling via JavaScript
        style property provides direct access to CSS properties
        Property names use camelCase (backgroundColor, not background-color)
        Inline styles have high specificity and override external CSS
        */
        newParagraph.style.color = 'green';

        /*
        CSS Class Management
        classList.add() adds a CSS class to the element
        Other methods: remove(), toggle(), contains()
        Preferred over className for class manipulation
        */
        newParagraph.classList.add('dynamic-content');

        /*
        DOM Tree Insertion
        appendChild() adds the element as the last child of the parent
        Other insertion methods: insertBefore(), prepend(), append()
        This is when the element becomes visible in the browser
        */
        firstSection.appendChild(newParagraph);
    }

    /*
    Example 3: Interactive Button Creation
    Demonstrates creating interactive elements programmatically
    Shows how to set up elements for later event handling
    */

    /*
    Button Element Creation
    Creates a new button element that can receive click events
    Buttons are inherently interactive and accessible
    */
    const specialButton = document.createElement('button');

    /*
    Text Content Assignment
    textContent is safer than innerHTML for plain text
    Automatically escapes HTML characters to prevent injection
    */
    specialButton.textContent = 'Click for Magic!';

    /*
    ID Assignment for Future Reference
    IDs must be unique within the document
    Allows easy element retrieval later in the code
    Essential for event handling and dynamic updates
    */
    specialButton.id = 'magicButton';

    /*
    Target Container Selection
    getElementById() is fastest method for ID-based selection
    Form section provides semantic context for the button
    */
    const formSection = document.getElementById('form-elements');

    if (formSection) {
        /*
        Element Insertion into Target Container
        Places the button within the form section
        Button becomes part of the document flow and receives styling
        */
        formSection.appendChild(specialButton);
    }

    /*
    ===========================================
    EVENT LISTENING AND HANDLING
    ===========================================
    Demonstrates various event types and handling patterns
    Events are the foundation of interactive web applications
    */

    /*
    Example 1: Button Click Event Handling
    Shows basic event listener attachment and response
    Demonstrates 'this' context in event handlers
    */

    /*
    Element Retrieval for Event Binding
    Gets reference to the previously created button
    Must ensure element exists before adding event listeners
    */
    const magicButton = document.getElementById('magicButton');

    if (magicButton) {
        /*
        Click Event Listener Registration
        addEventListener() is the modern, preferred method
        First parameter: event type ('click', 'mouseover', etc.)
        Second parameter: callback function to execute
        Third parameter (optional): options object or boolean for capture
        */
        magicButton.addEventListener('click', function() {
            /*
            User Feedback via Alert Dialog
            alert() creates a blocking modal dialog
            Simple but intrusive - consider modern alternatives like toast notifications
            Useful for demonstrations and simple confirmations
            */
            alert('Magic!');

            /*
            Dynamic Content Update Using 'this'
            'this' keyword refers to the element that triggered the event
            Provides direct access to the event target without additional queries
            Only works with regular functions, not arrow functions
            */
            this.textContent = 'Magic Happened!';

            /*
            Element State Modification
            disabled property prevents further user interaction
            Useful for preventing double-clicks or form resubmission
            Can be reversed by setting to false
            */
            this.disabled = true;

            /*
            Important Note: 'this' Context
            In regular functions, 'this' refers to the element that triggered the event
            In arrow functions, 'this' would refer to the enclosing scope
            Choose function type based on desired 'this' behavior
            */
        });
    }

    /*
    Example 2: Form Input Event Handling
    Demonstrates multiple event types on form elements
    Essential for creating responsive, interactive forms
    */

    /*
    Input Element Selection
    Targets the name input field from the HTML form
    Form inputs generate various events during user interaction
    */
    const nameInput = document.getElementById('name');

    if (nameInput) {
        /*
        Focus Event Handling
        Fires when element receives keyboard focus
        Useful for highlighting active fields or showing help text
        Triggered by clicking, tabbing, or programmatic focus()
        */
        nameInput.addEventListener('focus', () => {
            /*
            Console Logging for Development
            console.log() outputs to browser developer tools
            Essential for debugging and monitoring user interactions
            Should be removed or controlled in production code
            */
            console.log('Name input focused');

            /*
            Visual Feedback on Focus
            Changes background color to indicate active state
            Improves user experience and accessibility
            CSS :focus pseudo-class can also achieve this
            */
            nameInput.style.backgroundColor = 'lightyellow';
        });

        /*
        Blur Event Handling
        Fires when element loses keyboard focus
        Useful for validation, saving data, or resetting visual states
        Opposite of focus event
        */
        nameInput.addEventListener('blur', () => {
            console.log('Name input blurred');

            /*
            Visual State Reset
            Empty string removes the inline style
            Returns element to its CSS-defined appearance
            Good practice to clean up temporary visual changes
            */
            nameInput.style.backgroundColor = '';
        });

        /*
        Input Event Handling
        Fires whenever the input value changes
        More responsive than 'change' event which fires only on blur
        Perfect for real-time validation or search suggestions
        */
        nameInput.addEventListener('input', (event) => {
            /*
            Event Object Usage
            event.target refers to the element that triggered the event
            Provides access to current value, properties, and methods
            Alternative to using 'this' when using arrow functions
            */
            console.log(`Name input value changed: ${event.target.value}`);
        });
    }

    /*
    ===========================================
    EVENT PROPAGATION (BUBBLING AND CAPTURING)
    ===========================================
    Demonstrates how events travel through the DOM tree
    Understanding propagation is crucial for complex event handling
    */

    /*
    Element Selection for Propagation Demo
    Selects container and child elements to demonstrate event flow
    Uses different selector methods for variety
    */
    const flexContainer = document.querySelector('.flex-container');
    const flexItems = document.querySelectorAll('.flex-item');

    if (flexContainer) {
        /*
        Capturing Phase Event Listener
        Events first travel DOWN from document root to target (capturing)
        Third parameter 'true' specifies capturing phase
        Less commonly used but useful for intercepting events early
        */
        flexContainer.addEventListener('click', () => {
            /*
            Capturing Phase Logging
            This fires BEFORE the target element's listeners
            Useful for global event handling or preprocessing
            */
            console.log('Flex Container Clicked (Capturing)');

            /*
            Event Propagation Control (commented for demonstration)
            event.stopPropagation() would prevent further propagation
            Uncomment to see how it affects the event flow
            */
            // event.stopPropagation();
        }, true);

        /*
        Bubbling Phase Event Listener (Default Behavior)
        Events travel UP from target to document root (bubbling)
        Third parameter omitted or false specifies bubbling phase
        Most common pattern for event handling
        */
        flexContainer.addEventListener('click', (event) => {
            /*
            Bubbling Phase Logging
            This fires AFTER the target element's listeners
            Useful for parent element responses to child events
            */
            console.log('Flex Container Clicked (Bubbling)');

            /*
            Event Target vs Current Target
            event.target: The actual element that was clicked (may be a child)
            event.currentTarget: The element this listener is attached to (container)
            Understanding this difference is crucial for event delegation
            */
            console.log(`Event target: ${event.target.textContent}`);
            console.log(`Event currentTarget: ${event.currentTarget.className}`);
        });
    }

    /*
    Child Element Event Listeners
    Demonstrates how child events interact with parent listeners
    forEach() applies the same listener to multiple elements
    */
    flexItems.forEach(item => {
        /*
        Individual Item Click Handlers
        These fire during the bubbling phase at the target element
        Will trigger before the container's bubbling listener
        */
        item.addEventListener('click', () => {
            /*
            Target Element Response
            This executes first when a flex item is clicked
            Shows the specific item that was interacted with
            */
            console.log(`${item.textContent} Clicked (Bubbling)`);

            /*
            Propagation Control Option (commented)
            event.stopPropagation() would prevent bubbling to parent
            Useful when child should handle event exclusively
            */
            // event.stopPropagation();
        });
    });

    /*
    ===========================================
    EVENT DELEGATION PATTERN
    ===========================================
    Efficient technique for handling events on multiple similar elements
    Uses event bubbling to handle child events at parent level
    Particularly useful for dynamic content and performance optimization
    */

    /*
    Parent Container Selection
    Targets the section containing the list elements
    Event delegation works by listening on a common parent
    */
    const listElementsSection = document.getElementById('list-elements');

    if (listElementsSection) {
        /*
        Nested Element Selection
        Finds the unordered list within the section
        This will be our event delegation target
        */
        const unorderedList = listElementsSection.querySelector('ul');

        if (unorderedList) {
            /*
            Single Event Listener for Multiple Children
            Instead of adding listeners to each <li>, listen on parent <ul>
            More efficient and handles dynamically added items automatically
            */
            unorderedList.addEventListener('click', function(event) {
                /*
                Event Target Filtering
                Check if the clicked element matches our criteria
                tagName property returns uppercase element name
                Essential for delegation to work correctly
                */
                if (event.target.tagName === 'LI') {
                    /*
                    Delegated Event Processing
                    Extract text content from the clicked list item
                    firstChild.textContent gets the text node content
                    trim() removes leading/trailing whitespace
                    */
                    console.log(`Delegated click: You clicked on list item: ${event.target.firstChild.textContent.trim()}`);

                    /*
                    Dynamic Style Toggle
                    Toggles line-through decoration on clicked items
                    Ternary operator provides concise conditional logic
                    Demonstrates how delegation enables interactive features
                    */
                    event.target.style.textDecoration =
                        event.target.style.textDecoration === 'line-through' ? '' : 'line-through';
                }
            });
        }
    }

    /*
    ===========================================
    CUSTOM EVENTS EXAMPLE
    ===========================================
    Demonstrates creating and dispatching custom events
    Useful for component communication and decoupled architecture
    */

    /*
    Custom Event Trigger Button Creation
    Creates a button that will dispatch custom events
    Shows how to programmatically create interactive elements
    */
    const customEventButton = document.createElement('button');
    customEventButton.textContent = 'Trigger Custom Event';
    customEventButton.id = 'customEventTrigger';

    /*
    Button Insertion into DOM
    Adds the custom event button to the first section
    Reuses the firstSection variable from earlier
    */
    if (firstSection) {
        firstSection.appendChild(customEventButton);
    }

    /*
    Custom Event Listener Registration
    Listens for our custom event type anywhere in the document
    Custom events can carry additional data in the 'detail' property
    */
    document.addEventListener('myCustomEvent', function(event) {
        /*
        Custom Event Response
        Logs the custom event detection and processes the data
        event.detail contains custom data passed with the event
        */
        console.log('Custom event "myCustomEvent" caught!');
        console.log('Detail:', event.detail);

        /*
        User Notification with Custom Data
        Displays the custom message from the event detail
        Demonstrates how custom events can carry complex data
        */
        alert(`Custom Event Triggered: ${event.detail.message}`);
    });

    if (customEventButton) {
        /*
        Custom Event Dispatch Handler
        Click handler that creates and dispatches a custom event
        Shows the complete custom event lifecycle
        */
        customEventButton.addEventListener('click', () => {
            /*
            Custom Event Creation
            CustomEvent constructor creates events with custom data
            More flexible than standard Event constructor
            */
            const myEvent = new CustomEvent('myCustomEvent', {
                /*
                Custom Event Data
                detail property can contain any JavaScript object
                Provides a way to pass complex data with events
                */
                detail: {
                    message: 'Hello from the custom event!',
                    timestamp: new Date().toLocaleTimeString(),
                    /*
                    Additional data can be included:
                    userId: getCurrentUserId(),
                    action: 'button_click',
                    metadata: { source: 'demo', version: '1.0' }
                    */
                },

                /*
                Event Propagation Settings
                bubbles: true allows the event to bubble up through DOM
                cancelable: true allows preventDefault() to work
                */
                bubbles: true,
                cancelable: true
            });

            /*
            Event Dispatch
            dispatchEvent() triggers the custom event
            All registered listeners for this event type will fire
            Returns false if any listener called preventDefault()
            */
            customEventButton.dispatchEvent(myEvent);
        });
    }

    /*
    ===========================================
    ADVANCED FORM HANDLING EXAMPLE
    ===========================================
    Demonstrates modern form processing techniques
    Shows how to intercept and process form submissions with JavaScript
    */

    /*
    Form Element Selection
    Uses CSS selector to find the form within the form-elements section
    Descendant selector ensures we get the right form
    */
    const mainForm = document.querySelector('#form-elements form');

    if (mainForm) {
        /*
        Form Submit Event Listener
        Intercepts form submission to process data with JavaScript
        Essential for AJAX submissions and client-side validation
        */
        mainForm.addEventListener('submit', function(event) {
            /*
            Prevent Default Form Submission
            event.preventDefault() stops the browser's default form submission
            Allows JavaScript to handle the submission instead
            Essential for single-page applications and AJAX forms
            */
            event.preventDefault();
            console.log('Form submission intercepted by JavaScript.');

            /*
            FormData Object Creation
            FormData automatically collects all form field values
            Handles various input types including files
            'this' refers to the form element that triggered the event
            */
            const formData = new FormData(this);

            /*
            Form Data Processing and Display
            Iterates through all form fields and their values
            formData.entries() returns an iterator of [key, value] pairs
            Useful for debugging and data validation
            */
            let formOutput = 'Form Data:\n';
            for (const [key, value] of formData.entries()) {
                formOutput += `${key}: ${value}\n`;
            }
            console.log(formOutput);

            /*
            User Feedback
            Provides immediate feedback about form processing
            In production, this might be a success message or redirect
            */
            alert('Form submitted! Check the console for data. (Actual submission prevented)');

            /*
            Server Communication Example (commented)
            In real applications, form data is typically sent to a server
            fetch() API is the modern way to make HTTP requests
            FormData can be sent directly as the request body
            */
            // Example server submission:
            // fetch('/submit-form', {
            //     method: 'POST',
            //     body: formData
            // })
            // .then(response => response.json())
            // .then(data => console.log('Success:', data))
            // .catch(error => console.error('Error:', error));
        });
    }

    /*
    ===========================================
    RESPONSIVE JAVASCRIPT WITH MEDIA QUERIES
    ===========================================
    Demonstrates how to respond to screen size changes with JavaScript
    Complements CSS media queries for complex responsive behavior
    */

    /*
    Media Query Change Handler Function
    Responds to screen size changes detected by JavaScript media queries
    Allows for complex responsive behavior beyond CSS capabilities
    */
    function handleMediaQueryChange(mediaQuery) {
        /*
        Media Query Match Detection
        mediaQuery.matches returns true when the query conditions are met
        This specific query targets screens 768px wide or smaller
        */
        if (mediaQuery.matches) {
            /*
            Small Screen Detection Logging
            Provides feedback when mobile layout is activated
            Useful for debugging responsive behavior
            */
            console.log('Media query matched: Small screen detected');

            /*
            Dynamic Mobile Notification Creation
            Creates a visual indicator for mobile mode
            Demonstrates programmatic UI element creation
            */
            const mobileNotice = document.createElement('div');
            mobileNotice.id = 'mobile-notice';

            /*
            Mobile Notice Styling
            Applies comprehensive styling via JavaScript
            Creates a fixed bottom notification bar
            */
            mobileNotice.style.backgroundColor = '#ffeb3b';
            mobileNotice.style.color = '#333';
            mobileNotice.style.padding = '10px';
            mobileNotice.style.textAlign = 'center';
            mobileNotice.style.position = 'fixed';
            mobileNotice.style.bottom = '0';
            mobileNotice.style.left = '0';
            mobileNotice.style.right = '0';
            mobileNotice.style.zIndex = '1000';
            mobileNotice.style.boxShadow = '0 -2px 5px rgba(0,0,0,0.2)';
            mobileNotice.textContent = 'You are viewing in mobile mode';

            /*
            Duplicate Element Prevention
            Checks if notification already exists before adding
            Prevents multiple notifications from accumulating
            */
            if (!document.getElementById('mobile-notice')) {
                document.body.appendChild(mobileNotice);
            }

            /*
            Navigation Mobile Optimization
            Modifies navigation layout for mobile screens
            querySelectorAll() selects all matching elements
            */
            const navItems = document.querySelectorAll('header nav ul li');
            navItems.forEach(item => {
                /*
                Mobile Navigation Styling
                Changes from horizontal to vertical layout
                Increases spacing for touch interaction
                */
                item.style.display = 'block';
                item.style.margin = '10px 0';
            });

            /*
            Touch-Friendly Button Enhancement
            Makes buttons larger for easier touch interaction
            Targets multiple button types with compound selector
            */
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            allButtons.forEach(button => {
                /*
                Touch-Optimized Button Sizing
                Increases padding and font size for mobile usability
                Follows mobile UI best practices
                */
                button.style.padding = '15px 20px';
                button.style.fontSize = '1.1em';
            });

        } else {
            /*
            Large Screen Detection
            Handles the case when screen size exceeds mobile threshold
            Resets mobile-specific modifications
            */
            console.log('Media query not matched: Large screen detected');

            /*
            Mobile Notification Cleanup
            Removes the mobile notification when returning to desktop
            Demonstrates proper cleanup of dynamic elements
            */
            const mobileNotice = document.getElementById('mobile-notice');
            if (mobileNotice) {
                mobileNotice.remove();
            }

            /*
            Navigation Reset
            Restores original navigation styling for desktop
            Empty string removes inline styles, returning to CSS defaults
            */
            const navItems = document.querySelectorAll('header nav ul li');
            navItems.forEach(item => {
                item.style.display = '';
                item.style.margin = '';
            });

            /*
            Button Styling Reset
            Removes mobile-specific button enhancements
            Returns buttons to their CSS-defined appearance
            */
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            allButtons.forEach(button => {
                button.style.padding = '';
                button.style.fontSize = '';
            });
        }
    }
    
    /*
    Media Query List Creation and Setup
    window.matchMedia() creates a MediaQueryList object
    Allows JavaScript to respond to CSS media query changes
    */
    const mediaQuery = window.matchMedia('(max-width: 768px)');

    /*
    Initial Media Query Check
    Runs the handler immediately to set up initial state
    Ensures proper layout regardless of initial screen size
    */
    handleMediaQueryChange(mediaQuery);

    /*
    Media Query Change Listener Registration
    Responds to screen size changes in real-time
    Uses try/catch for cross-browser compatibility
    */
    try {
        /*
        Modern Event Listener Approach
        addEventListener() is the standard method in modern browsers
        Provides consistent event handling interface
        */
        mediaQuery.addEventListener('change', handleMediaQueryChange);
    } catch (e) {
        /*
        Legacy Browser Fallback
        addListener() is deprecated but still needed for older browsers
        Ensures functionality across wider browser support range
        */
        mediaQuery.addListener(handleMediaQueryChange);
    }
    
    // Additional media query for very small screens (mobile phones)
    const smallScreenQuery = window.matchMedia('(max-width: 480px)');
    
    // Function to handle very small screen changes
    function handleSmallScreenChange(mediaQuery) {
        if (mediaQuery.matches) { // Screen width <= 480px
            console.log('Very small screen detected');
            
            // Add a class to the body for potential CSS targeting
            document.body.classList.add('very-small-screen');
            
            // Increase font size for better readability on small screens
            document.body.style.fontSize = '16px';
            
            // Stack form elements vertically with more spacing
            const formDivs = document.querySelectorAll('#form-elements form div');
            formDivs.forEach(div => {
                div.style.marginBottom = '20px';
            });
            
        } else { // Screen width > 480px
            console.log('Not a very small screen');
            
            // Remove small screen specific changes
            document.body.classList.remove('very-small-screen');
            document.body.style.fontSize = '';
            
            // Reset form spacing
            const formDivs = document.querySelectorAll('#form-elements form div');
            formDivs.forEach(div => {
                div.style.marginBottom = '';
            });
        }
    }
    
    // Initial check for small screens
    handleSmallScreenChange(smallScreenQuery);
    
    // Add listener for small screen changes
    try {
        smallScreenQuery.addEventListener('change', handleSmallScreenChange);
    } catch (e) {
        smallScreenQuery.addListener(handleSmallScreenChange);
    }
    
    /*
    ===========================================
    DEVELOPMENT AND TESTING UTILITIES
    ===========================================
    Tools for testing responsive behavior and debugging
    Should be removed or disabled in production environments
    */

    /*
    Mobile View Testing Button Creation
    Creates a floating button for testing mobile layouts on desktop
    Useful for developers to test responsive behavior without resizing browser
    */
    const mobileTestButton = document.createElement('button');
    mobileTestButton.textContent = 'Toggle Mobile View (For Testing)';
    mobileTestButton.id = 'mobile-test-button';

    /*
    Testing Button Positioning and Styling
    Fixed positioning keeps button visible during scrolling
    High z-index ensures it stays above other content
    */
    mobileTestButton.style.position = 'fixed';
    mobileTestButton.style.top = '10px';
    mobileTestButton.style.right = '10px';
    mobileTestButton.style.zIndex = '1001';
    mobileTestButton.style.backgroundColor = '#4CAF50';
    mobileTestButton.style.color = 'white';
    mobileTestButton.style.border = 'none';
    mobileTestButton.style.borderRadius = '4px';
    mobileTestButton.style.padding = '8px 12px';
    mobileTestButton.style.cursor = 'pointer';

    /*
    Testing Button DOM Insertion
    Adds the button to the document body
    Makes it immediately available for testing
    */
    document.body.appendChild(mobileTestButton);

    /*
    Mobile View State Tracking
    Boolean flag to track whether mobile view is artificially enabled
    Prevents conflicts between real and simulated responsive states
    */
    let mobileViewForced = false;

    /*
    Mobile View Toggle Event Handler
    Allows developers to test mobile layouts without changing screen size
    Demonstrates manual event triggering and state management
    */
    mobileTestButton.addEventListener('click', () => {
        /*
        State Toggle Logic
        Logical NOT operator (!) flips the boolean value
        Simple way to alternate between two states
        */
        mobileViewForced = !mobileViewForced;

        if (mobileViewForced) {
            /*
            Force Mobile View Activation
            Manually triggers media query handlers with mobile parameters
            Simulates small screen conditions for testing
            */
            handleMediaQueryChange({ matches: true });
            handleSmallScreenChange({ matches: window.innerWidth <= 480 });

            /*
            Testing Button Visual Feedback
            Changes button appearance to indicate active test mode
            Red color suggests "exit" or "stop" action
            */
            mobileTestButton.textContent = 'Exit Mobile View Test';
            mobileTestButton.style.backgroundColor = '#f44336';
        } else {
            /*
            Return to Natural Responsive Behavior
            Triggers handlers with actual screen size conditions
            window.innerWidth provides current viewport width
            */
            handleMediaQueryChange({ matches: window.innerWidth <= 768 });
            handleSmallScreenChange({ matches: window.innerWidth <= 480 });

            /*
            Reset Button to Default State
            Returns button to original appearance and text
            Green color suggests "start" or "activate" action
            */
            mobileTestButton.textContent = 'Toggle Mobile View (For Testing)';
            mobileTestButton.style.backgroundColor = '#4CAF50';
        }
    });

    /*
    JavaScript Initialization Complete Notification
    Logs completion message to browser console
    Useful for debugging and confirming script execution
    Should be removed or made conditional in production
    */
    console.log('Advanced JavaScript examples loaded and executed.');

    /*
    End of DOMContentLoaded Event Handler
    All code above executes after DOM is ready
    Ensures reliable access to HTML elements
    */
});