<?php
// Include required files
require_once 'config.php'; // Configuration settings
require_once 'user.php';   // User class for authentication
require_once 'db.php';     // Database class for data retrieval

// Create instances of User and Database classes
$user = new User();
$db = Database::getInstance();

// ===== CHECK IF USER IS LOGGED IN =====
// If not, redirect to the login page
if (!$user->isLoggedIn()) {
    header('Location: login.php'); // Redirect to login
    exit(); // Stop script execution
}

// ===== GET CURRENT USER DATA =====
// Retrieve information about the logged-in user
$currentUser = $user->getCurrentUser();

// ===== HANDLE LOGOUT REQUEST =====
// Check if the logout parameter is in the URL
if (isset($_GET['logout'])) {
    $user->logout(); // Log the user out
    header('Location: login.php'); // Redirect to login
    exit(); // Stop script execution
}

// ===== FETCH EXAMPLE DATA FROM DATABASE =====
// This demonstrates how to retrieve data from a database table
$exampleData = $db->fetchAll("SELECT * FROM example_table LIMIT 10");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">                           <!-- Character encoding -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- Responsive viewport -->
    <title>Dashboard - PHP & MySQL Example</title>   <!-- Page title -->
    <link rel="stylesheet" href="styles.css">      <!-- Link to external CSS -->
    <style>
        /* ===== CONTAINER STYLES ===== */
        .dashboard-container {
            max-width: 1000px;                     /* Maximum width of the container */
            margin: 20px auto;                     /* Center the container with top margin */
            padding: 20px;                         /* Inner spacing */
            background-color: #fff;                /* White background */
            border-radius: 5px;                    /* Rounded corners */
            box-shadow: 0 0 10px rgba(0,0,0,0.1);  /* Subtle shadow effect */
        }
        
        /* ===== USER INFO STYLES ===== */
        .user-info {
            margin-bottom: 20px;                   /* Space below user info section */
            padding-bottom: 20px;                  /* Inner bottom padding */
            border-bottom: 1px solid #eee;         /* Light gray bottom border */
        }
        
        /* ===== TABLE STYLES ===== */
        .data-table {
            width: 100%;                           /* Full width table */
            border-collapse: collapse;             /* Collapse borders between cells */
            margin-top: 20px;                      /* Space above table */
        }
        .data-table th, .data-table td {
            padding: 10px;                         /* Cell padding */
            border: 1px solid #ddd;                /* Light gray cell borders */
            text-align: left;                      /* Left-align text */
        }
        .data-table th {
            background-color: #f0f0f0;              /* Light gray background for headers */
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;              /* Zebra striping for rows */
        }
        
        /* ===== BUTTON STYLES ===== */
        .btn {
            background-color: #337ab7;             /* Blue button background */
            color: white;                          /* White text */
            padding: 8px 12px;                     /* Inner spacing */
            border: none;                          /* No border */
            border-radius: 4px;                    /* Rounded corners */
            cursor: pointer;                       /* Hand cursor on hover */
            text-decoration: none;                 /* No underline for link buttons */
            display: inline-block;                 /* Allow setting margins */
            margin-right: 10px;                    /* Right margin */
        }
        .btn-danger {
            background-color: #d9534f;              /* Red background for danger buttons */
        }
        
        /* ===== NAVIGATION STYLES ===== */
        .nav {
            display: flex;                         /* Flexbox layout */
            justify-content: space-between;         /* Space items apart */
            align-items: center;                   /* Vertically center items */
            margin-bottom: 20px;                   /* Space below nav */
        }
    </style>
</head>
<body>
    <!-- Main container for the dashboard -->
    <div class="dashboard-container">
        <!-- Navigation bar with title and logout button -->
        <div class="nav">
            <h2>Dashboard</h2>
            <a href="?logout=1" class="btn btn-danger">Logout</a>
        </div>
        
        <!-- User information section -->
        <div class="user-info">
            <h3>Welcome, <?php echo htmlspecialchars($currentUser['username']); ?>!</h3>
            <p>Email: <?php echo htmlspecialchars($currentUser['email']); ?></p>
            <p>Account created: <?php echo htmlspecialchars($currentUser['created_at']); ?></p>
            <p>Last login: <?php echo htmlspecialchars($currentUser['last_login'] ?? 'N/A'); ?></p>
        </div>
        
        <!-- Example data section -->
        <h3>Example Data</h3>
        
        <?php if (empty($exampleData)): ?>
            <!-- Display instructions if no data is available -->
            <p>No data available. This is just a placeholder for database content.</p>
            <p>To use this example, you would need to:</p>
            <ol>
                <li>Create a MySQL database named 'test_db'</li>
                <li>Create a 'users' table with fields: id, username, email, password, created_at, last_login</li>
                <li>Create an 'example_table' for demonstration purposes</li>
            </ol>
        <?php else: ?>
            <!-- Display data table if data is available -->
            <table class="data-table">
                <thead>
                    <tr>
                        <?php foreach (array_keys($exampleData[0]) as $column): ?>
                            <th><?php echo htmlspecialchars($column); ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($exampleData as $row): ?>
                        <tr>
                            <?php foreach ($row as $value): ?>
                                <td><?php echo htmlspecialchars($value); ?></td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
    
    <!-- Link to JavaScript file -->
    <script src="script.js"></script>
</body>
</html>