# JavaScript DOM & Events Comprehensive Guide

This comprehensive guide covers JavaScript DOM manipulation, event handling, and essential browser APIs for modern web development.

## DOCUMENT OBJECT MODEL (DOM)

### What is the DOM?

The Document Object Model (DOM) is a programming interface for HTML and XML documents. It represents the page structure as a tree of objects that can be manipulated with JavaScript.

**DOM Tree Structure:**
```
Document
└── html
    ├── head
    │   ├── title
    │   └── meta
    └── body
        ├── header
        ├── main
        │   ├── section
        │   └── article
        └── footer
```

### DOM Node Types

- **Element Nodes**: HTML elements (`<div>`, `<p>`, `<span>`)
- **Text Nodes**: Text content within elements
- **Attribute Nodes**: Element attributes (`id`, `class`, `src`)
- **Comment Nodes**: HTML comments (`<!-- comment -->`)
- **Document Node**: The root document object

## DOM SELECTION METHODS

### Basic Selection Methods

#### getElementById()
```javascript
// Select element by ID (returns single element or null)
const header = document.getElementById('main-header');
console.log(header); // <header id="main-header">...</header>

// Common use case
if (header) {
    header.style.color = 'blue';
}
```

#### getElementsByClassName()
```javascript
// Select elements by class name (returns HTMLCollection)
const buttons = document.getElementsByClassName('btn');
console.log(buttons.length); // Number of elements with class 'btn'

// Convert to array for modern methods
const buttonArray = Array.from(buttons);
buttonArray.forEach(button => {
    button.addEventListener('click', handleClick);
});
```

#### getElementsByTagName()
```javascript
// Select elements by tag name (returns HTMLCollection)
const paragraphs = document.getElementsByTagName('p');
const images = document.getElementsByTagName('img');

// Access specific element
const firstParagraph = paragraphs[0];
```

### Modern Selection Methods (Recommended)

#### querySelector()
```javascript
// Select first matching element using CSS selector syntax
const firstButton = document.querySelector('.btn');
const emailInput = document.querySelector('input[type="email"]');
const navLink = document.querySelector('nav a:first-child');

// Complex selectors
const specificElement = document.querySelector('#container .card:nth-child(2) h3');
```

#### querySelectorAll()
```javascript
// Select all matching elements (returns NodeList)
const allButtons = document.querySelectorAll('.btn');
const formInputs = document.querySelectorAll('form input');

// NodeList has forEach method
allButtons.forEach((button, index) => {
    button.textContent = `Button ${index + 1}`;
});

// Convert to array if needed
const inputArray = [...formInputs];
```

### Advanced Selection Techniques

#### Traversing the DOM
```javascript
const element = document.querySelector('.target');

// Parent relationships
const parent = element.parentNode;
const parentElement = element.parentElement;

// Child relationships
const children = element.children; // HTMLCollection of child elements
const childNodes = element.childNodes; // NodeList including text nodes
const firstChild = element.firstElementChild;
const lastChild = element.lastElementChild;

// Sibling relationships
const nextSibling = element.nextElementSibling;
const prevSibling = element.previousElementSibling;
```

#### Closest() Method
```javascript
// Find closest ancestor matching selector
const button = document.querySelector('.delete-btn');
const card = button.closest('.card');
const form = button.closest('form');

// Useful for event delegation
button.addEventListener('click', (e) => {
    const listItem = e.target.closest('li');
    if (listItem) {
        listItem.remove();
    }
});
```

## DOM MANIPULATION

### Creating Elements

#### createElement()
```javascript
// Create new elements
const div = document.createElement('div');
const paragraph = document.createElement('p');
const button = document.createElement('button');

// Set attributes and content
div.className = 'container';
div.id = 'new-container';
paragraph.textContent = 'This is a new paragraph';
button.innerHTML = '<span>Click me</span>';

// Create complex structure
const article = document.createElement('article');
article.innerHTML = `
    <h2>Article Title</h2>
    <p>Article content goes here...</p>
    <button class="read-more">Read More</button>
`;
```

#### DocumentFragment for Performance
```javascript
// Use DocumentFragment for multiple elements
const fragment = document.createDocumentFragment();

for (let i = 0; i < 100; i++) {
    const listItem = document.createElement('li');
    listItem.textContent = `Item ${i + 1}`;
    fragment.appendChild(listItem);
}

// Single DOM update (better performance)
document.querySelector('ul').appendChild(fragment);
```

### Inserting Elements

#### appendChild()
```javascript
const container = document.querySelector('.container');
const newElement = document.createElement('div');

// Add as last child
container.appendChild(newElement);
```

#### insertBefore()
```javascript
const container = document.querySelector('.container');
const newElement = document.createElement('div');
const referenceElement = container.querySelector('.reference');

// Insert before reference element
container.insertBefore(newElement, referenceElement);
```

#### Modern Insertion Methods
```javascript
const targetElement = document.querySelector('.target');
const newElement = document.createElement('div');

// Insert at different positions
targetElement.before(newElement);        // Before target
targetElement.after(newElement);         // After target
targetElement.prepend(newElement);       // First child of target
targetElement.append(newElement);        // Last child of target

// Insert multiple elements
targetElement.append(element1, element2, 'text content');
```

#### insertAdjacentHTML()
```javascript
const element = document.querySelector('.target');

// Insert HTML at specific positions
element.insertAdjacentHTML('beforebegin', '<div>Before element</div>');
element.insertAdjacentHTML('afterbegin', '<p>First child</p>');
element.insertAdjacentHTML('beforeend', '<p>Last child</p>');
element.insertAdjacentHTML('afterend', '<div>After element</div>');
```

### Modifying Content

#### textContent vs innerHTML
```javascript
const element = document.querySelector('.content');

// textContent: Safe, plain text only
element.textContent = 'Safe text content';
element.textContent = '<script>alert("XSS")</script>'; // Rendered as text

// innerHTML: Powerful but potentially unsafe
element.innerHTML = '<strong>Bold text</strong>'; // Renders HTML
element.innerHTML = userInput; // DANGEROUS with user input

// innerText: Similar to textContent but respects styling
element.innerText = 'Visible text only';
```

#### Safe HTML Insertion
```javascript
// Use textContent for user input
function displayUserComment(comment) {
    const commentElement = document.createElement('div');
    commentElement.textContent = comment; // Safe from XSS
    return commentElement;
}

// Sanitize HTML if needed
function sanitizeHTML(html) {
    const temp = document.createElement('div');
    temp.textContent = html;
    return temp.innerHTML;
}
```

### Removing Elements

#### remove() Method
```javascript
const element = document.querySelector('.to-remove');
element.remove(); // Modern way to remove element
```

#### removeChild() Method
```javascript
const parent = document.querySelector('.parent');
const child = document.querySelector('.child');
parent.removeChild(child); // Older method
```

#### Clearing Content
```javascript
const container = document.querySelector('.container');

// Remove all children
container.innerHTML = ''; // Fast but loses event listeners
container.textContent = ''; // Also removes all content

// Remove children while preserving event listeners
while (container.firstChild) {
    container.removeChild(container.firstChild);
}
```

## ELEMENT ATTRIBUTES AND PROPERTIES

### Working with Attributes

#### getAttribute() and setAttribute()
```javascript
const image = document.querySelector('img');

// Get attributes
const src = image.getAttribute('src');
const alt = image.getAttribute('alt');
const customAttr = image.getAttribute('data-id');

// Set attributes
image.setAttribute('src', 'new-image.jpg');
image.setAttribute('alt', 'New description');
image.setAttribute('data-category', 'photos');

// Remove attributes
image.removeAttribute('data-old-attr');

// Check if attribute exists
if (image.hasAttribute('data-id')) {
    console.log('Element has data-id attribute');
}
```

#### Direct Property Access
```javascript
const input = document.querySelector('input');

// Common properties (faster than getAttribute)
input.value = 'New value';
input.type = 'email';
input.placeholder = 'Enter email';
input.disabled = true;
input.checked = true; // For checkboxes/radio buttons

// Boolean attributes
input.required = true;
input.readonly = false;
```

### CSS Class Management

#### className Property
```javascript
const element = document.querySelector('.target');

// Get all classes as string
console.log(element.className); // "class1 class2 class3"

// Set classes (overwrites existing)
element.className = 'new-class another-class';

// Add class (manual string manipulation)
element.className += ' additional-class';
```

#### classList API (Recommended)
```javascript
const element = document.querySelector('.target');

// Add classes
element.classList.add('active');
element.classList.add('highlight', 'important'); // Multiple classes

// Remove classes
element.classList.remove('inactive');
element.classList.remove('old-class', 'deprecated'); // Multiple classes

// Toggle class
element.classList.toggle('visible'); // Add if not present, remove if present
element.classList.toggle('active', true); // Force add
element.classList.toggle('active', false); // Force remove

// Check if class exists
if (element.classList.contains('active')) {
    console.log('Element is active');
}

// Replace class
element.classList.replace('old-class', 'new-class');

// Get all classes as array-like object
console.log(element.classList); // DOMTokenList
```

### CSS Styling

#### Inline Styles
```javascript
const element = document.querySelector('.target');

// Set individual styles
element.style.color = 'red';
element.style.backgroundColor = 'yellow';
element.style.fontSize = '16px';
element.style.marginTop = '20px';

// CSS properties with hyphens use camelCase
element.style.borderRadius = '5px';
element.style.textAlign = 'center';

// Set multiple styles
Object.assign(element.style, {
    width: '200px',
    height: '100px',
    border: '1px solid black',
    padding: '10px'
});

// Remove styles
element.style.color = '';
element.style.removeProperty('background-color');
```

#### CSS Custom Properties (Variables)
```javascript
const element = document.querySelector('.target');

// Set CSS custom properties
element.style.setProperty('--main-color', '#3498db');
element.style.setProperty('--font-size', '18px');

// Get CSS custom properties
const mainColor = element.style.getPropertyValue('--main-color');

// Set on document root for global variables
document.documentElement.style.setProperty('--theme-color', '#e74c3c');
```

#### Computed Styles
```javascript
const element = document.querySelector('.target');

// Get computed styles (read-only)
const computedStyle = window.getComputedStyle(element);
const color = computedStyle.color;
const fontSize = computedStyle.fontSize;
const marginTop = computedStyle.marginTop;

// Get specific property
const backgroundColor = window.getComputedStyle(element).backgroundColor;

// Pseudo-elements
const beforeStyle = window.getComputedStyle(element, '::before');
const beforeContent = beforeStyle.content;

## EVENT HANDLING

### Event Fundamentals

Events are actions that occur in the browser, such as user interactions, page loading, or timer completions. JavaScript can respond to these events to create interactive web applications.

#### Common Event Types
- **Mouse Events**: click, dblclick, mousedown, mouseup, mouseover, mouseout, mousemove
- **Keyboard Events**: keydown, keyup, keypress
- **Form Events**: submit, change, input, focus, blur
- **Window Events**: load, resize, scroll, unload
- **Touch Events**: touchstart, touchmove, touchend (mobile)

### Adding Event Listeners

#### addEventListener() Method (Recommended)
```javascript
const button = document.querySelector('.btn');

// Basic event listener
button.addEventListener('click', function() {
    console.log('Button clicked!');
});

// Arrow function syntax
button.addEventListener('click', () => {
    console.log('Button clicked with arrow function!');
});

// Named function (can be removed later)
function handleClick() {
    console.log('Button clicked with named function!');
}
button.addEventListener('click', handleClick);

// Event listener with options
button.addEventListener('click', handleClick, {
    once: true,      // Execute only once
    passive: true,   // Never calls preventDefault()
    capture: true    // Execute during capture phase
});
```

#### Event Handler Properties
```javascript
const button = document.querySelector('.btn');

// Direct assignment (older method)
button.onclick = function() {
    console.log('Button clicked!');
};

// Can only have one handler per event type
button.onclick = handleClick; // Overwrites previous handler
```

#### Inline Event Handlers (Not Recommended)
```html
<!-- HTML inline handlers (avoid in modern development) -->
<button onclick="handleClick()">Click me</button>
<input onchange="handleInputChange(this.value)">
```

### Event Object

Every event handler receives an event object containing information about the event.

#### Event Object Properties
```javascript
button.addEventListener('click', function(event) {
    // Event type
    console.log(event.type); // "click"

    // Target element (where event originated)
    console.log(event.target); // The clicked element

    // Current target (element with event listener)
    console.log(event.currentTarget); // The button element

    // Mouse position
    console.log(event.clientX, event.clientY); // Relative to viewport
    console.log(event.pageX, event.pageY); // Relative to document
    console.log(event.screenX, event.screenY); // Relative to screen

    // Modifier keys
    console.log(event.ctrlKey); // true if Ctrl key pressed
    console.log(event.shiftKey); // true if Shift key pressed
    console.log(event.altKey); // true if Alt key pressed

    // Prevent default behavior
    event.preventDefault();

    // Stop event propagation
    event.stopPropagation();
});
```

#### Keyboard Event Properties
```javascript
document.addEventListener('keydown', function(event) {
    console.log(event.key); // The key value ("a", "Enter", "ArrowUp")
    console.log(event.code); // Physical key code ("KeyA", "Enter", "ArrowUp")
    console.log(event.keyCode); // Deprecated numeric code
    console.log(event.which); // Deprecated, similar to keyCode

    // Check for specific keys
    if (event.key === 'Enter') {
        console.log('Enter key pressed');
    }

    if (event.key === 'Escape') {
        console.log('Escape key pressed');
    }

    // Modifier combinations
    if (event.ctrlKey && event.key === 's') {
        event.preventDefault(); // Prevent browser save
        console.log('Ctrl+S pressed');
    }
});
```

### Event Propagation

Events in the DOM follow a specific propagation pattern: capture phase, target phase, and bubble phase.

#### Event Bubbling
```javascript
// HTML structure: <div><button>Click</button></div>
const div = document.querySelector('div');
const button = document.querySelector('button');

// Event bubbles from target to root
button.addEventListener('click', () => {
    console.log('Button clicked'); // Executes first
});

div.addEventListener('click', () => {
    console.log('Div clicked'); // Executes second (bubbling)
});

document.addEventListener('click', () => {
    console.log('Document clicked'); // Executes third
});
```

#### Event Capturing
```javascript
// Capture phase (root to target)
div.addEventListener('click', () => {
    console.log('Div clicked (capture)'); // Executes first
}, true); // true enables capture

button.addEventListener('click', () => {
    console.log('Button clicked'); // Executes second
});
```

#### Stopping Propagation
```javascript
button.addEventListener('click', function(event) {
    console.log('Button clicked');
    event.stopPropagation(); // Prevents bubbling to parent elements
});

// stopImmediatePropagation() also stops other listeners on same element
button.addEventListener('click', function(event) {
    console.log('First listener');
    event.stopImmediatePropagation();
});

button.addEventListener('click', function(event) {
    console.log('Second listener'); // Won't execute
});
```

### Event Delegation

Event delegation uses event bubbling to handle events for multiple elements with a single event listener.

#### Basic Event Delegation
```javascript
// Instead of adding listeners to each button
const container = document.querySelector('.button-container');

container.addEventListener('click', function(event) {
    // Check if clicked element is a button
    if (event.target.matches('button')) {
        console.log('Button clicked:', event.target.textContent);
    }

    // Handle specific button types
    if (event.target.classList.contains('delete-btn')) {
        handleDelete(event.target);
    }

    if (event.target.classList.contains('edit-btn')) {
        handleEdit(event.target);
    }
});
```

#### Advanced Event Delegation
```javascript
// Handle dynamically added elements
const todoList = document.querySelector('.todo-list');

todoList.addEventListener('click', function(event) {
    const target = event.target;
    const todoItem = target.closest('.todo-item');

    if (!todoItem) return; // Click wasn't on a todo item

    if (target.matches('.complete-btn')) {
        toggleComplete(todoItem);
    } else if (target.matches('.delete-btn')) {
        deleteTodo(todoItem);
    } else if (target.matches('.edit-btn')) {
        editTodo(todoItem);
    }
});

// Functions work with dynamically added todos
function addTodo(text) {
    const todoItem = document.createElement('div');
    todoItem.className = 'todo-item';
    todoItem.innerHTML = `
        <span class="todo-text">${text}</span>
        <button class="complete-btn">Complete</button>
        <button class="edit-btn">Edit</button>
        <button class="delete-btn">Delete</button>
    `;
    todoList.appendChild(todoItem);
}
```

### Removing Event Listeners

#### removeEventListener()
```javascript
const button = document.querySelector('.btn');

function handleClick() {
    console.log('Button clicked');
}

// Add listener
button.addEventListener('click', handleClick);

// Remove listener (must use same function reference)
button.removeEventListener('click', handleClick);

// Anonymous functions cannot be removed
button.addEventListener('click', function() {
    console.log('Cannot remove this listener');
});
```

#### AbortController (Modern Approach)
```javascript
const controller = new AbortController();
const signal = controller.signal;

// Add multiple listeners with same signal
button.addEventListener('click', handleClick, { signal });
input.addEventListener('input', handleInput, { signal });
window.addEventListener('resize', handleResize, { signal });

// Remove all listeners at once
controller.abort();

// Check if signal is aborted
if (signal.aborted) {
    console.log('Event listeners have been removed');
}
```

### Form Events

#### Form Submission
```javascript
const form = document.querySelector('form');

form.addEventListener('submit', function(event) {
    event.preventDefault(); // Prevent default form submission

    // Get form data
    const formData = new FormData(form);

    // Access individual fields
    const email = formData.get('email');
    const password = formData.get('password');

    // Validate form
    if (!email || !password) {
        showError('Please fill in all fields');
        return;
    }

    // Submit via AJAX
    submitForm(formData);
});
```

#### Input Events
```javascript
const input = document.querySelector('input');

// Input event (fires on every change)
input.addEventListener('input', function(event) {
    console.log('Current value:', event.target.value);
    validateInput(event.target.value);
});

// Change event (fires when input loses focus)
input.addEventListener('change', function(event) {
    console.log('Final value:', event.target.value);
});

// Focus and blur events
input.addEventListener('focus', function() {
    this.classList.add('focused');
});

input.addEventListener('blur', function() {
    this.classList.remove('focused');
    validateField(this);
});
```

### Custom Events

#### Creating Custom Events
```javascript
// Create custom event
const customEvent = new CustomEvent('userLogin', {
    detail: {
        username: 'john_doe',
        timestamp: Date.now()
    },
    bubbles: true,
    cancelable: true
});

// Dispatch custom event
document.dispatchEvent(customEvent);

// Listen for custom event
document.addEventListener('userLogin', function(event) {
    console.log('User logged in:', event.detail.username);
    console.log('Timestamp:', event.detail.timestamp);
});
```

#### Event-Driven Architecture
```javascript
class EventEmitter {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }

    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }
}

// Usage
const emitter = new EventEmitter();

emitter.on('dataLoaded', (data) => {
    console.log('Data received:', data);
});

emitter.emit('dataLoaded', { users: [], posts: [] });
```

## BROWSER APIS AND ADVANCED CONCEPTS

### Window and Document Objects

#### Window Object
```javascript
// Window properties
console.log(window.innerWidth, window.innerHeight); // Viewport size
console.log(window.outerWidth, window.outerHeight); // Browser window size
console.log(window.location.href); // Current URL
console.log(window.navigator.userAgent); // Browser info

// Window methods
window.open('https://example.com', '_blank'); // Open new window
window.close(); // Close current window
window.print(); // Print page
window.scrollTo(0, 100); // Scroll to position
window.scrollBy(0, 50); // Scroll by amount

// Timers
const timeoutId = setTimeout(() => {
    console.log('Executed after 1 second');
}, 1000);

const intervalId = setInterval(() => {
    console.log('Executed every 2 seconds');
}, 2000);

// Clear timers
clearTimeout(timeoutId);
clearInterval(intervalId);
```

#### Document Object
```javascript
// Document properties
console.log(document.title); // Page title
console.log(document.URL); // Current URL
console.log(document.domain); // Domain name
console.log(document.readyState); // loading, interactive, complete

// Document methods
document.write('<p>Dynamic content</p>'); // Not recommended
document.writeln('Content with line break');

// Document events
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM fully loaded');
});

window.addEventListener('load', () => {
    console.log('Page fully loaded including images');
});
```

### Local Storage and Session Storage

#### localStorage
```javascript
// Store data (persists until manually cleared)
localStorage.setItem('username', 'john_doe');
localStorage.setItem('preferences', JSON.stringify({
    theme: 'dark',
    language: 'en'
}));

// Retrieve data
const username = localStorage.getItem('username');
const preferences = JSON.parse(localStorage.getItem('preferences'));

// Remove data
localStorage.removeItem('username');
localStorage.clear(); // Remove all data

// Check if storage is available
function isStorageAvailable(type) {
    try {
        const storage = window[type];
        const test = '__storage_test__';
        storage.setItem(test, test);
        storage.removeItem(test);
        return true;
    } catch (e) {
        return false;
    }
}

if (isStorageAvailable('localStorage')) {
    // Use localStorage
}
```

#### sessionStorage
```javascript
// Store data (persists for session only)
sessionStorage.setItem('tempData', 'session value');

// Same API as localStorage
const tempData = sessionStorage.getItem('tempData');
sessionStorage.removeItem('tempData');
sessionStorage.clear();

// Storage event (fires when storage changes in other tabs)
window.addEventListener('storage', (event) => {
    console.log('Storage changed:', event.key, event.newValue);
});
```

### Fetch API and AJAX

#### Fetch API
```javascript
// Basic GET request
fetch('https://api.example.com/users')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Users:', data);
    })
    .catch(error => {
        console.error('Fetch error:', error);
    });

// POST request with data
fetch('https://api.example.com/users', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123'
    },
    body: JSON.stringify({
        name: 'John Doe',
        email: '<EMAIL>'
    })
})
.then(response => response.json())
.then(data => console.log('Created user:', data));

// Async/await syntax
async function fetchUsers() {
    try {
        const response = await fetch('https://api.example.com/users');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const users = await response.json();
        return users;
    } catch (error) {
        console.error('Error fetching users:', error);
        throw error;
    }
}
```

#### XMLHttpRequest (Legacy)
```javascript
function makeRequest(url, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.open(method, url);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                resolve(JSON.parse(xhr.responseText));
            } else {
                reject(new Error(`Request failed with status ${xhr.status}`));
            }
        };

        xhr.onerror = function() {
            reject(new Error('Network error'));
        };

        xhr.send(data ? JSON.stringify(data) : null);
    });
}

// Usage
makeRequest('https://api.example.com/users')
    .then(users => console.log(users))
    .catch(error => console.error(error));
```

### Intersection Observer API

#### Basic Intersection Observer
```javascript
// Create observer for lazy loading images
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src; // Load actual image
            img.classList.remove('lazy');
            observer.unobserve(img); // Stop observing this image
        }
    });
}, {
    threshold: 0.1, // Trigger when 10% visible
    rootMargin: '50px' // Start loading 50px before entering viewport
});

// Observe all lazy images
document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
});
```

#### Infinite Scroll Implementation
```javascript
const infiniteScrollObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            loadMoreContent();
        }
    });
});

// Observe sentinel element at bottom of content
const sentinel = document.querySelector('.scroll-sentinel');
infiniteScrollObserver.observe(sentinel);

async function loadMoreContent() {
    try {
        const response = await fetch(`/api/content?page=${currentPage}`);
        const newContent = await response.json();

        newContent.forEach(item => {
            const element = createContentElement(item);
            contentContainer.appendChild(element);
        });

        currentPage++;
    } catch (error) {
        console.error('Failed to load more content:', error);
    }
}
```

### Mutation Observer API

#### Observing DOM Changes
```javascript
// Create mutation observer
const mutationObserver = new MutationObserver((mutations) => {
    mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    console.log('Element added:', node);
                    initializeNewElement(node);
                }
            });

            mutation.removedNodes.forEach(node => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    console.log('Element removed:', node);
                    cleanupElement(node);
                }
            });
        }

        if (mutation.type === 'attributes') {
            console.log('Attribute changed:', mutation.attributeName);
        }
    });
});

// Start observing
mutationObserver.observe(document.body, {
    childList: true,     // Observe child additions/removals
    subtree: true,       // Observe all descendants
    attributes: true,    // Observe attribute changes
    attributeOldValue: true, // Include old attribute values
    characterData: true  // Observe text content changes
});

// Stop observing
mutationObserver.disconnect();
```

## PERFORMANCE OPTIMIZATION

### DOM Performance Best Practices

#### Minimize DOM Access
```javascript
// Bad: Multiple DOM queries
function updateList(items) {
    const list = document.querySelector('.list');
    items.forEach(item => {
        const li = document.createElement('li');
        li.textContent = item.name;
        list.appendChild(li); // Multiple DOM modifications
    });
}

// Good: Batch DOM operations
function updateListOptimized(items) {
    const list = document.querySelector('.list');
    const fragment = document.createDocumentFragment();

    items.forEach(item => {
        const li = document.createElement('li');
        li.textContent = item.name;
        fragment.appendChild(li);
    });

    list.appendChild(fragment); // Single DOM modification
}
```

#### Efficient Event Handling
```javascript
// Bad: Multiple event listeners
function attachListeners() {
    document.querySelectorAll('.button').forEach(button => {
        button.addEventListener('click', handleClick);
    });
}

// Good: Event delegation
function attachListenersOptimized() {
    document.addEventListener('click', (event) => {
        if (event.target.matches('.button')) {
            handleClick(event);
        }
    });
}
```

#### Debouncing and Throttling
```javascript
// Debounce: Execute after delay, reset timer on new calls
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Throttle: Execute at most once per interval
function throttle(func, interval) {
    let lastCall = 0;
    return function(...args) {
        const now = Date.now();
        if (now - lastCall >= interval) {
            lastCall = now;
            func.apply(this, args);
        }
    };
}

// Usage
const debouncedSearch = debounce(performSearch, 300);
const throttledScroll = throttle(handleScroll, 100);

searchInput.addEventListener('input', debouncedSearch);
window.addEventListener('scroll', throttledScroll);
```

### Memory Management

#### Avoiding Memory Leaks
```javascript
// Bad: Creates memory leak
function createLeakyHandler() {
    const largeData = new Array(1000000).fill('data');

    document.addEventListener('click', function() {
        // This closure keeps largeData in memory
        console.log(largeData.length);
    });
}

// Good: Clean up references
function createCleanHandler() {
    const largeData = new Array(1000000).fill('data');

    function clickHandler() {
        console.log('Clicked');
        // Don't reference largeData unnecessarily
    }

    document.addEventListener('click', clickHandler);

    // Clean up when needed
    return function cleanup() {
        document.removeEventListener('click', clickHandler);
    };
}

// WeakMap for private data (doesn't prevent garbage collection)
const privateData = new WeakMap();

class MyClass {
    constructor(data) {
        privateData.set(this, data);
    }

    getData() {
        return privateData.get(this);
    }
}
```

## BEST PRACTICES AND PATTERNS

### Modern JavaScript Patterns

#### Module Pattern
```javascript
// IIFE Module Pattern
const UserModule = (function() {
    let users = [];

    function addUser(user) {
        users.push(user);
    }

    function getUsers() {
        return [...users]; // Return copy
    }

    function removeUser(id) {
        users = users.filter(user => user.id !== id);
    }

    // Public API
    return {
        add: addUser,
        getAll: getUsers,
        remove: removeUser
    };
})();

// ES6 Modules
// userModule.js
export class UserManager {
    constructor() {
        this.users = [];
    }

    addUser(user) {
        this.users.push(user);
    }

    getUsers() {
        return [...this.users];
    }
}

// main.js
import { UserManager } from './userModule.js';
const userManager = new UserManager();
```

#### Observer Pattern
```javascript
class Observable {
    constructor() {
        this.observers = [];
    }

    subscribe(observer) {
        this.observers.push(observer);
    }

    unsubscribe(observer) {
        this.observers = this.observers.filter(obs => obs !== observer);
    }

    notify(data) {
        this.observers.forEach(observer => observer.update(data));
    }
}

class Observer {
    constructor(name) {
        this.name = name;
    }

    update(data) {
        console.log(`${this.name} received:`, data);
    }
}

// Usage
const observable = new Observable();
const observer1 = new Observer('Observer 1');
const observer2 = new Observer('Observer 2');

observable.subscribe(observer1);
observable.subscribe(observer2);
observable.notify('Hello observers!');
```

### Error Handling

#### Try-Catch with Async/Await
```javascript
async function handleAsyncOperation() {
    try {
        const response = await fetch('/api/data');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        if (error instanceof TypeError) {
            console.error('Network error:', error.message);
        } else if (error instanceof SyntaxError) {
            console.error('JSON parsing error:', error.message);
        } else {
            console.error('Unexpected error:', error.message);
        }

        // Re-throw or handle appropriately
        throw error;
    }
}
```

#### Global Error Handling
```javascript
// Catch unhandled errors
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // Log to error reporting service
    logError(event.error);
});

// Catch unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // Prevent default browser behavior
    event.preventDefault();
});
```

This comprehensive guide covers the essential concepts of JavaScript DOM manipulation and event handling, providing the foundation for building interactive web applications with modern best practices and performance optimization techniques.
