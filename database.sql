-- Create the database
-- IF NOT EXISTS prevents errors if the database already exists
-- CHARACTER SET utf8mb4 supports all Unicode characters including emojis
-- COLLATE utf8mb4_unicode_ci sets case-insensitive Unicode collation
CREATE DATABASE IF NOT EXISTS test_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Select the database for subsequent operations
USE test_db;

-- Users table
-- This table stores user account information
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,           -- Unique identifier, automatically incremented
    username VARCHAR(50) NOT NULL UNIQUE,        -- Username (required, must be unique)
    email VARCHAR(100) NOT NULL UNIQUE,          -- Email address (required, must be unique)
    password VARCHAR(255) NOT NULL,              -- Password hash (255 chars for bcrypt/Argon2)
    created_at DATETIME NOT NULL,                -- Account creation timestamp
    last_login DATETIME NULL                     -- Last login timestamp (can be null)
);

-- Example table for demonstration
-- This table demonstrates a relationship with the users table
CREATE TABLE IF NOT EXISTS example_table (
    id INT AUTO_INCREMENT PRIMARY KEY,           -- Unique identifier, automatically incremented
    title VARCHAR(100) NOT NULL,                 -- Title field (required)
    description TEXT NULL,                       -- Description field (optional)
    created_at DATETIME NOT NULL,                -- Creation timestamp
    user_id INT,                                 -- Foreign key to users table
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL  -- Relationship with users table
);

-- Insert some example data
-- These records will be visible in the dashboard when the database is set up
INSERT INTO example_table (title, description, created_at, user_id)
VALUES 
('Example 1', 'This is the first example', NOW(), NULL),  -- NOW() inserts the current date and time
('Example 2', 'This is the second example', NOW(), NULL),
('Example 3', 'This is the third example', NOW(), NULL);