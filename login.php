<?php
// Include required files
require_once 'config.php'; // Configuration settings
require_once 'user.php';   // User class for authentication

// Create a new User object
$user = new User();

// Initialize variables for error and success messages
$error = '';
$success = '';

// ===== CHECK IF USER IS ALREADY LOGGED IN =====
// If so, redirect to the dashboard
if ($user->isLoggedIn()) {
    header('Location: dashboard.php'); // Redirect to dashboard
    exit(); // Stop script execution
}

// ===== PROCESS LOGIN FORM SUBMISSION =====
// Check if the form was submitted using POST method
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data with null coalescing operator (??) for PHP 7+
    // trim() removes whitespace from the beginning and end of the string
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? ''; // No trim for password as spaces might be part of it
    
    // Validate form data - check if fields are empty
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password';
    } else {
        // Attempt to log in the user
        $result = $user->login($username, $password);
        
        // Check login result
        if ($result['success']) {
            // Login successful, redirect to dashboard
            header('Location: dashboard.php');
            exit();
        } else {
            // Login failed, store error message
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">                           <!-- Character encoding -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- Responsive viewport -->
    <title>Login - PHP & MySQL Example</title>      <!-- Page title -->
    <link rel="stylesheet" href="styles.css">      <!-- Link to external CSS -->
    <style>
        /* ===== CONTAINER STYLES ===== */
        .login-container {
            max-width: 400px;                      /* Maximum width of the container */
            margin: 50px auto;                     /* Center the container with top margin */
            padding: 20px;                         /* Inner spacing */
            background-color: #fff;                /* White background */
            border-radius: 5px;                    /* Rounded corners */
            box-shadow: 0 0 10px rgba(0,0,0,0.1);  /* Subtle shadow effect */
        }
        
        /* ===== FORM GROUP STYLES ===== */
        .form-group {
            margin-bottom: 15px;                   /* Space between form groups */
        }
        .form-group label {
            display: block;                         /* Make labels block elements */
            margin-bottom: 5px;                    /* Space below labels */
            font-weight: bold;                     /* Bold text for labels */
        }
        .form-group input {
            width: 100%;                           /* Full width inputs */
            padding: 8px;                          /* Inner spacing in inputs */
            border: 1px solid #ddd;                /* Light gray border */
            border-radius: 4px;                    /* Rounded corners for inputs */
            box-sizing: border-box;                /* Include padding in width calculation */
        }
        
        /* ===== BUTTON STYLES ===== */
        .btn {
            background-color: #337ab7;             /* Blue button background */
            color: white;                          /* White text */
            padding: 10px 15px;                    /* Inner spacing */
            border: none;                          /* No border */
            border-radius: 4px;                    /* Rounded corners */
            cursor: pointer;                       /* Hand cursor on hover */
        }
        
        /* ===== MESSAGE STYLES ===== */
        .error {
            color: red;                            /* Red text for errors */
            margin-bottom: 15px;                   /* Space below error messages */
        }
        .success {
            color: green;                          /* Green text for success */
            margin-bottom: 15px;                   /* Space below success messages */
        }
    </style>
</head>
<body>
    <!-- Main container for the login form -->
    <div class="login-container">
        <h2>Login</h2>
        
        <!-- Display error message if there is one -->
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Display success message if there is one -->
        <?php if (!empty($success)): ?>
            <div class="success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <!-- Login form - submits to the same page (empty action) -->
        <form method="post" action="">
            <!-- Username field -->
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <!-- Password field -->
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <!-- Submit button -->
            <div class="form-group">
                <button type="submit" class="btn">Login</button>
            </div>
            
            <!-- Link to registration page -->
            <p>Don't have an account? <a href="register.php">Register here</a></p>
        </form>
    </div>
    
    <!-- Link to JavaScript file -->
    <script src="script.js"></script>
</body>
</html>