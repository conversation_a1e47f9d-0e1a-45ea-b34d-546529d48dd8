<!--
    HTML5 Document Declaration
    This declares the document as HTML5, ensuring modern browser compatibility
    and enabling the use of HTML5 semantic elements and features
-->
<!DOCTYPE html>

<!--
    Root HTML element with language attribute
    The lang="en" attribute helps screen readers and search engines
    understand the document's primary language
-->
<html lang="en">

<!--
    Document Head Section
    Contains metadata, links to external resources, and document configuration
    Content in the head is not displayed on the page but provides important information
-->
<head>
    <!--
        Character Encoding Declaration
        UTF-8 supports all Unicode characters, ensuring proper display of international text
        This should be the first element in the head for proper parsing
    -->
    <meta charset="UTF-8">

    <!--
        Viewport Meta Tag for Responsive Design
        Controls how the page is displayed on mobile devices
        width=device-width: Sets the width to match the device's screen width
        initial-scale=1.0: Sets the initial zoom level when the page loads
    -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--
        SEO Meta Tags
        These help search engines understand and index the page content
    -->
    <!-- Page description that appears in search engine results (150-160 characters recommended) -->
    <meta name="description" content="Comprehensive HTML and CSS examples">

    <!-- Keywords for search engine optimization (less important in modern SEO) -->
    <meta name="keywords" content="HTML, CSS, Flexbox, Grid, Web Development">

    <!-- Author information -->
    <meta name="author" content="AI Assistant">

    <!--
        Page Title
        Appears in browser tab, bookmarks, and search engine results
        Should be descriptive and unique for each page
    -->
    <title>HTML & CSS Comprehensive Guide</title>

    <!--
        External Stylesheet Link
        Links to the CSS file that styles this HTML document
        rel="stylesheet" specifies the relationship type
        href points to the CSS file location
    -->
    <link rel="stylesheet" href="styles.css">
</head>

<!--
    Document Body
    Contains all visible content of the webpage
    Everything inside body will be rendered in the browser
-->
<body>
    <!--
        Header Element (HTML5 Semantic)
        Represents introductory content or navigational aids
        Typically contains site title, logo, and main navigation
        Should be used once per page or section
    -->
    <header>
        <!--
            Main Heading (h1)
            The most important heading on the page
            Should be used only once per page for SEO and accessibility
            Represents the main topic or purpose of the page
        -->
        <h1>HTML & CSS Comprehensive Guide</h1>

        <!--
            Navigation Element (HTML5 Semantic)
            Contains navigation links for the site or page sections
            Helps screen readers and search engines identify navigation areas
        -->
        <nav>
            <!--
                Unordered List for Navigation
                Semantically appropriate for navigation menus
                Each list item contains a navigation link
            -->
            <ul>
                <!--
                    Navigation Links with Fragment Identifiers
                    href="#id" creates internal page links that scroll to elements with matching IDs
                    These create a "table of contents" style navigation
                -->
                <li><a href="#text-elements">Text Elements</a></li>
                <li><a href="#list-elements">Lists</a></li>
                <li><a href="#media-elements">Media</a></li>
                <li><a href="#form-elements">Forms</a></li>
                <li><a href="#table-elements">Tables</a></li>
                <li><a href="#semantic-elements">Semantic HTML</a></li>
                <li><a href="#flexbox-container">Flexbox</a></li>
                <li><a href="#grid-container">Grid</a></li>
            </ul>
        </nav>
    </header>

    <!--
        Main Content Area (HTML5 Semantic)
        Contains the primary content of the page
        Should be unique to each page and not contain repeated content like headers/footers
        There should be only one main element per page
    -->
    <main>
        <!--
            Text Elements Section
            Demonstrates various HTML text formatting and semantic elements
            The id attribute allows this section to be targeted by navigation links and CSS
        -->
        <section id="text-elements">
            <!--
                Section Heading (h2)
                Second-level heading that describes this section's content
                Creates a hierarchical structure for accessibility and SEO
            -->
            <h2>Text Elements</h2>

            <!--
                Paragraph with Inline Span
                p: Block-level element for text paragraphs
                span: Inline element for styling specific text without semantic meaning
            -->
            <p>This is a paragraph. <span>This is a span inside a paragraph.</span></p>

            <!--
                Text Emphasis Elements
                strong: Indicates strong importance (typically bold)
                em: Indicates emphasis (typically italic)
                Both have semantic meaning beyond visual styling
            -->
            <p><strong>This text is strong (bold).</strong> <em>This text is emphasized (italic).</em></p>

            <!--
                Text Decoration Elements
                u: Underlined text (use sparingly, can be confused with links)
                mark: Highlighted text, indicates relevance or importance
            -->
            <p><u>This text is underlined.</u> <mark>This text is marked.</mark></p>

            <!--
                Text Modification Elements
                small: Smaller text, often for fine print or side comments
                del: Deleted text, shows content that has been removed
                ins: Inserted text, shows content that has been added
            -->
            <p><small>This is small text.</small> <del>This text is deleted.</del> <ins>This text is inserted.</ins></p>

            <!--
                Subscript and Superscript
                sub: Subscript text (below baseline) - useful for chemical formulas
                sup: Superscript text (above baseline) - useful for mathematical expressions
            -->
            <p>H<sub>2</sub>O (subscript) and E=MC<sup>2</sup> (superscript).</p>

            <!--
                Inline Quotation
                q: Short inline quotations
                Browsers typically add quotation marks automatically
            -->
            <p>This is a <q>short inline quotation</q>.</p>

            <!--
                Block Quotation with Citation
                blockquote: For longer quotations that are set apart from main text
                cite: Indicates the source or author of the quotation
            -->
            <blockquote>
                <p>This is a blockquote. It's used for longer quotations that are set off from the main text.</p>
                <cite>Source</cite>
            </blockquote>

            <!--
                Abbreviation with Title Attribute
                abbr: Marks abbreviations or acronyms
                title attribute provides the full expansion on hover
                Improves accessibility for screen readers
            -->
            <abbr title="HyperText Markup Language">HTML</abbr>

            <!--
                Address Element
                address: Contact information for the author or owner
                Typically displayed in italics
                br: Line break element for formatting addresses
            -->
            <address>
                Written by AI Assistant<br>
                Visit us at:<br>
                example.com<br>
                Box 564, Disneyland<br>
                USA
            </address>

            <!--
                Preformatted Text with Code
                pre: Preserves whitespace and line breaks
                code: Indicates computer code
                Together they're perfect for displaying code snippets
            -->
            <pre>
                <code>
function greet() {
  console.log("Hello, world!");
}
                </code>
            </pre>

            <!--
                Definition Element
                dfn: Marks the defining instance of a term
                Useful for glossaries or when introducing new terminology
            -->
            <p>The <dfn>definition element</dfn> is used to indicate the defining instance of a new term.</p>

            <!--
                Deletion and Insertion Example
                Shows how content changes can be marked up
                Useful for showing document revisions
            -->
            <p>My favorite color is <del>blue</del> <ins>red</ins>!</p>

            <!--
                Keyboard Input Element
                kbd: Represents user input, typically keyboard keys
                Useful for documentation and tutorials
            -->
            <p>Press <kbd>Ctrl</kbd> + <kbd>C</kbd> to copy text.</p>

            <!--
                Sample Output Element
                samp: Represents sample output from a computer program
                Distinguishes program output from regular text
            -->
            <p>The program is generating <samp>Sample output from a computer program</samp>.</p>

            <!--
                Variable Element
                var: Represents a variable in mathematical expressions or programming
                Typically displayed in italics
            -->
            <p>The variable <var>x</var> represents the unknown quantity.</p>
        </section>

        <!--
            List Elements Section
            Demonstrates the three main types of HTML lists
            Lists are essential for organizing related content
        -->
        <section id="list-elements">
            <h2>List Elements</h2>

            <!-- Subsection heading for unordered lists -->
            <h3>Unordered List</h3>

            <!--
                Unordered List (ul)
                Creates a bulleted list where order doesn't matter
                Default styling shows bullet points
                Can be nested for hierarchical content
            -->
            <ul>
                <!--
                    List Items (li)
                    Each item in the list
                    Can contain any HTML content including other lists
                -->
                <li>Item 1</li>
                <li>Item 2
                    <!--
                        Nested Unordered List
                        Lists can be nested inside list items
                        Creates sub-levels of information
                        Browsers typically change bullet style for nested lists
                    -->
                    <ul>
                        <li>Sub-item 2.1</li>
                        <li>Sub-item 2.2</li>
                    </ul>
                </li>
                <li>Item 3</li>
            </ul>

            <!-- Subsection heading for ordered lists -->
            <h3>Ordered List</h3>

            <!--
                Ordered List (ol)
                Creates a numbered list where order matters
                start attribute: Specifies the starting number (default is 1)
                Other attributes: type (1, A, a, I, i), reversed
            -->
            <ol start="5">
                <li>First item (starts at 5)</li>
                <li>Second item</li>
                <li>Third item</li>
            </ol>

            <!-- Subsection heading for description lists -->
            <h3>Description List</h3>

            <!--
                Description List (dl)
                Used for name-value pairs, definitions, or metadata
                Perfect for glossaries, FAQs, or key-value data
                More semantic than using regular lists for definitions
            -->
            <dl>
                <!--
                    Description Term (dt)
                    The term, name, or key being defined
                    Can have multiple dt elements for one dd
                -->
                <dt>HTML</dt>

                <!--
                    Description Details (dd)
                    The definition, value, or description
                    Can have multiple dd elements for one dt
                -->
                <dd>HyperText Markup Language</dd>

                <dt>CSS</dt>
                <dd>Cascading Style Sheets</dd>
            </dl>
        </section>

        <!--
            Media Elements Section
            Demonstrates HTML5 media elements for images, audio, and video
            Modern browsers support these elements natively
        -->
        <section id="media-elements">
            <h2>Media Elements</h2>

            <!-- Subsection for basic image element -->
            <h3>Image</h3>

            <!--
                Image Element (img)
                Self-closing element for displaying images
                src: Source URL of the image (required)
                alt: Alternative text for accessibility and when image fails to load (required)
                width/height: Dimensions in pixels (optional, can be set via CSS)
                Always include alt text for screen readers and SEO
            -->
            <img src="https://via.placeholder.com/150" alt="Placeholder Image" width="150" height="150">

            <!-- Subsection for semantic figure element -->
            <h3>Figure with Caption</h3>

            <!--
                Figure Element (HTML5 Semantic)
                Groups media content with its caption
                Represents self-contained content that can be moved without affecting main flow
                Perfect for images, diagrams, code listings, etc.
            -->
            <figure>
                <!-- Image within figure -->
                <img src="https://via.placeholder.com/300x200" alt="Another Placeholder Image">

                <!--
                    Figure Caption (figcaption)
                    Provides a caption or legend for the figure
                    Should be the first or last child of figure element
                    Improves accessibility and provides context
                -->
                <figcaption>Fig.1 - A placeholder image with a caption.</figcaption>
            </figure>

            <!-- Subsection for audio element -->
            <h3>Audio</h3>

            <!--
                Audio Element (HTML5)
                Embeds audio content in the page
                controls: Shows play/pause/volume controls to user
                Other attributes: autoplay, loop, muted, preload
            -->
            <audio controls>
                <!--
                    Source Element
                    Specifies multiple media resources for audio/video elements
                    Browser will use the first supported format
                    type: MIME type of the media file
                -->
                <source src="#" type="audio/mpeg"> <!-- Replace # with actual audio file -->

                <!--
                    Fallback Content
                    Displayed if browser doesn't support audio element
                    Important for older browsers
                -->
                Your browser does not support the audio element.
            </audio>

            <!-- Subsection for video element -->
            <h3>Video</h3>

            <!--
                Video Element (HTML5)
                Embeds video content in the page
                width/height: Video dimensions
                controls: Shows video controls to user
                poster: Image shown before video plays
                Other attributes: autoplay, loop, muted, preload
            -->
            <video width="320" height="240" controls poster="https://via.placeholder.com/320x240/000000/FFFFFF/?text=Video+Poster">
                <!-- Video source with MIME type -->
                <source src="#" type="video/mp4"> <!-- Replace # with actual video file -->

                <!--
                    Track Element
                    Provides text tracks for video (subtitles, captions, descriptions)
                    label: User-readable title for the track
                    kind: Type of track (subtitles, captions, descriptions, chapters, metadata)
                    srclang: Language of the track text
                    src: URL of the track file (WebVTT format)
                    default: Makes this track active by default
                -->
                <track label="English" kind="subtitles" srclang="en" src="#" default> <!-- Replace # with actual subtitles file -->

                <!-- Fallback content for unsupported browsers -->
                Your browser does not support the video tag.
            </video>
        </section>

        <!--
            Form Elements Section
            Demonstrates comprehensive HTML form controls and validation
            Forms are essential for user interaction and data collection
        -->
        <section id="form-elements">
            <h2>Form Elements</h2>

            <!--
                Form Element
                Container for all form controls
                action: URL where form data is sent when submitted
                method: HTTP method (get/post) - post is more secure for sensitive data
                Other attributes: enctype, target, autocomplete, novalidate
            -->
            <form action="#" method="post">
                <!--
                    Fieldset Element
                    Groups related form controls together
                    Provides semantic structure and can be styled as a group
                    Useful for accessibility and organization
                -->
                <fieldset>
                    <!--
                        Legend Element
                        Provides a caption for the fieldset
                        Describes the group of form controls
                        Important for screen readers
                    -->
                    <legend>Personal Information</legend>

                    <!--
                        Form Control Container
                        div elements group label and input for styling and structure
                        Each form control should have an associated label
                    -->
                    <div>
                        <!--
                            Label Element
                            Associates text with form controls
                            for attribute must match the id of the associated control
                            Clicking the label focuses the associated control
                            Essential for accessibility
                        -->
                        <label for="name">Name:</label>

                        <!--
                            Text Input
                            type="text": Basic text input
                            id: Unique identifier (matches label's for attribute)
                            name: Sent to server when form is submitted
                            required: HTML5 validation - field must be filled
                            placeholder: Hint text shown when field is empty
                        -->
                        <input type="text" id="name" name="username" required placeholder="Enter your name">
                    </div>

                    <div>
                        <label for="email">Email:</label>
                        <!--
                            Email Input (HTML5)
                            Provides built-in email validation
                            Mobile browsers show email-optimized keyboard
                            Browser validates format automatically
                        -->
                        <input type="email" id="email" name="useremail" required placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="password">Password:</label>
                        <!--
                            Password Input
                            Masks input characters for security
                            minlength: Minimum number of characters required
                            Can also use maxlength, pattern attributes
                        -->
                        <input type="password" id="password" name="userpassword" required minlength="8">
                    </div>

                    <div>
                        <label for="tel">Telephone:</label>
                        <!--
                            Telephone Input (HTML5)
                            pattern: Regular expression for validation
                            Mobile browsers may show numeric keypad
                            Format validation helps ensure data quality
                        -->
                        <input type="tel" id="tel" name="usertel" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************">
                    </div>

                    <div>
                        <label for="url">Website:</label>
                        <!--
                            URL Input (HTML5)
                            Validates URL format automatically
                            Mobile browsers show URL-optimized keyboard
                        -->
                        <input type="url" id="url" name="userurl" placeholder="https://example.com">
                    </div>

                    <div>
                        <label for="search">Search:</label>
                        <!--
                            Search Input (HTML5)
                            Semantically indicates search functionality
                            May show clear button in some browsers
                            Can trigger search-specific browser behaviors
                        -->
                        <input type="search" id="search" name="usersearch">
                    </div>

                    <div>
                        <label for="number">Number (1-10):</label>
                        <!--
                            Number Input (HTML5)
                            min/max: Define valid range
                            step: Increment/decrement value
                            Shows spinner controls in most browsers
                            Validates numeric input automatically
                        -->
                        <input type="number" id="number" name="usernumber" min="1" max="10" step="1">
                    </div>

                    <div>
                        <label for="range">Range (0-100):</label>
                        <!--
                            Range Input (HTML5)
                            Creates a slider control
                            value: Default/current value
                            min/max: Define the range
                            step: Increment between values
                        -->
                        <input type="range" id="range" name="userrange" min="0" max="100" value="50">

                        <!--
                            Output Element (HTML5)
                            Displays the result of a calculation or user action
                            for attribute links it to the associated input
                            Updated via JavaScript to show current range value
                        -->
                        <output for="range" id="range-output">50</output>
                    </div>
                    <div>
                        <label for="date">Date:</label>
                        <input type="date" id="date" name="userdate">
                    </div>
                    <div>
                        <label for="time">Time:</label>
                        <input type="time" id="time" name="usertime">
                    </div>
                    <div>
                        <label for="datetime-local">Date and Time:</label>
                        <input type="datetime-local" id="datetime-local" name="userdatetime">
                    </div>
                    <div>
                        <label for="month">Month:</label>
                        <input type="month" id="month" name="usermonth">
                    </div>
                    <div>
                        <label for="week">Week:</label>
                        <input type="week" id="week" name="userweek">
                    </div>
                    <div>
                        <label for="color">Color:</label>
                        <input type="color" id="color" name="usercolor" value="#e66465">
                    </div>
                </fieldset>

                <fieldset>
                    <legend>Choices</legend>
                    <p>Gender:</p>
                    <input type="radio" id="male" name="gender" value="male">
                    <label for="male">Male</label><br>
                    <input type="radio" id="female" name="gender" value="female">
                    <label for="female">Female</label><br>
                    <input type="radio" id="other" name="gender" value="other">
                    <label for="other">Other</label>

                    <p>Interests:</p>
                    <input type="checkbox" id="coding" name="interest" value="coding">
                    <label for="coding">Coding</label><br>
                    <input type="checkbox" id="music" name="interest" value="music">
                    <label for="music">Music</label><br>
                    <input type="checkbox" id="sports" name="interest" value="sports">
                    <label for="sports">Sports</label>
                </fieldset>

                <fieldset>
                    <legend>Other Inputs</legend>
                    <div>
                        <label for="message">Message:</label>
                        <textarea id="message" name="usermessage" rows="5" cols="30" placeholder="Write your message here..."></textarea>
                    </div>
                    <div>
                        <label for="cars">Choose a car:</label>
                        <select id="cars" name="usercar">
                            <optgroup label="Swedish Cars">
                                <option value="volvo">Volvo</option>
                                <option value="saab">Saab</option>
                            </optgroup>
                            <optgroup label="German Cars">
                                <option value="mercedes">Mercedes</option>
                                <option value="audi" selected>Audi</option>
                            </optgroup>
                        </select>
                    </div>
                    <div>
                        <label for="browser">Choose your browser from the list:</label>
                        <input list="browsers" id="browser" name="userbrowser">
                        <datalist id="browsers">
                            <option value="Edge">
                            <option value="Firefox">
                            <option value="Chrome">
                            <option value="Opera">
                            <option value="Safari">
                        </datalist>
                    </div>
                    <div>
                        <label for="file">Upload a file:</label>
                        <input type="file" id="file" name="userfile" accept="image/png, image/jpeg">
                    </div>
                </fieldset>

                <button type="submit">Submit</button>
                <button type="reset">Reset</button>
                <input type="button" value="Input Button">
                <button type="button" onclick="alert('Button Clicked!')">Click Me Button</button>
            </form>
        </section>

        <section id="table-elements">
            <h2>Table Elements</h2>
            <table>
                <caption>Monthly Savings</caption>
                <colgroup>
                    <col span="1" style="background-color: #f2f2f2;">
                    <col span="2" style="background-color: #e0e0e0;">
                </colgroup>
                <thead>
                    <tr>
                        <th>Month</th>
                        <th>Savings</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>January</td>
                        <td>$100</td>
                        <td rowspan="2">Good start</td>
                    </tr>
                    <tr>
                        <td>February</td>
                        <td>$80</td>
                    </tr>
                    <tr>
                        <td>March</td>
                        <td colspan="2">$120 (Combined with April)</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2">Total Savings</td>
                        <td>$300</td>
                    </tr>
                </tfoot>
            </table>
        </section>

        <section id="semantic-elements">
            <h2>Semantic HTML Elements</h2>
            <article>
                <h3>Article Title</h3>
                <p>This is an article. It represents a self-contained composition in a document, page, application, or site, which is intended to be independently distributable or reusable.</p>
                <details>
                    <summary>More details about the article</summary>
                    <p>This is some additional information hidden by default.</p>
                </details>
            </article>
            <aside>
                <h4>Aside Content</h4>
                <p>This is an aside. It represents a portion of a document whose content is only indirectly related to the document's main content.</p>
            </aside>
            <section>
                <h3>Generic Section</h3>
                <p>This is a generic section, used to group related content.</p>
            </section>
            <time datetime="2024-07-23T10:00:00Z">July 23, 2024, 10:00 AM UTC</time>
            <br>
            <progress value="70" max="100">70%</progress>
            <meter value="2" min="0" max="10" low="3" high="7" optimum="8">2 out of 10</meter>
        </section>

        <section id="flexbox-container" class="flexbox-container-section">
            <h2>Flexbox Example</h2>
            <div class="flex-container">
                <div class="flex-item item1">Item 1</div>
                <div class="flex-item item2">Item 2 (longer content)</div>
                <div class="flex-item item3">Item 3</div>
                <div class="flex-item item4">Item 4</div>
                <div class="flex-item item5">Item 5</div>
            </div>
        </section>

        <section id="grid-container" class="grid-container-section">
            <h2>Grid Example</h2>
            <div class="grid-container">
                <div class="grid-item gitem1">Grid Item 1 (Header)</div>
                <div class="grid-item gitem2">Grid Item 2 (Sidebar)</div>
                <div class="grid-item gitem3">Grid Item 3 (Main Content)</div>
                <div class="grid-item gitem4">Grid Item 4 (Footer)</div>
                <div class="grid-item gitem5">Grid Item 5</div>
                <div class="grid-item gitem6">Grid Item 6</div>
            </div>
        </section>

    </main>

    <!--
        Footer Element (HTML5 Semantic)
        Contains footer information for the page or section
        Typically includes copyright, contact info, and secondary navigation
        Should be used once per page or section
    -->
    <footer>
        <!--
            Copyright Notice
            &copy; is an HTML entity for the copyright symbol
            Provides legal information about content ownership
        -->
        <p>&copy; 2024 HTML & CSS Guide. All rights reserved.</p>

        <!--
            Back to Top Link
            href="#top" would scroll to an element with id="top"
            Since no such element exists, this will scroll to the top of the page
            Useful for long pages to improve user experience
        -->
        <p><a href="#top">Back to top</a></p>
    </footer>

    <!--
        Inline JavaScript
        Embedded directly in the HTML document
        For small scripts or when external files aren't needed
        Should be placed before closing body tag for performance
    -->
    <script>
        // Basic JavaScript to update range output
        // This demonstrates how HTML, CSS, and JavaScript work together

        // Get references to the range input and output elements
        const rangeInput = document.getElementById('range');
        const rangeOutput = document.getElementById('range-output');

        // Check if both elements exist before adding functionality
        if (rangeInput && rangeOutput) {
            // Set initial output value to match input value
            rangeOutput.textContent = rangeInput.value;

            // Add event listener to update output when range changes
            rangeInput.addEventListener('input', () => {
                rangeOutput.textContent = rangeInput.value;
            });
        }
    </script>
</body>
</html>