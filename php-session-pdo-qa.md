# PHP Session Management & PDO Q&A Guide

This comprehensive Q&A guide focuses on PHP session management and PDO (PHP Data Objects), covering theory, practical coding, security, and real-world implementation scenarios.

## SESSION MANAGEMENT QUESTIONS

### Basic Session Theory

**Q1: What are PHP sessions and how do they work?**
A: PHP sessions are a way to store user data across multiple pages. Sessions work by:
- Creating a unique session ID (PHPSESSID by default)
- Storing the session ID in a cookie on the client
- Storing session data on the server (files, database, memory)
- Associating client requests with server-side data via the session ID
- Data persists until session expires or is destroyed

**Q2: What's the difference between sessions and cookies?**
A:
- **Sessions**: Data stored on server, more secure, larger storage, expires when browser closes
- **Cookies**: Data stored on client, less secure, limited size (4KB), can have long expiration
- **Security**: Sessions are safer for sensitive data
- **Performance**: Cookies reduce server load but increase bandwidth

**Q3: Explain the session lifecycle in PHP.**
A: Session lifecycle stages:
1. **Start**: `session_start()` creates or resumes session
2. **Write**: Data stored in `$_SESSION` superglobal
3. **Read**: Data retrieved from `$_SESSION` on subsequent requests
4. **Update**: Modify `$_SESSION` data as needed
5. **Destroy**: `session_destroy()` removes all session data
6. **Garbage Collection**: Automatic cleanup of expired sessions

### Session Configuration and Security

**Q4: What are the most important session security configurations?**
A: Critical session security settings:
```php
// Prevent JavaScript access to session cookie
ini_set('session.cookie_httponly', 1);

// Only send cookie over HTTPS
ini_set('session.cookie_secure', 1);

// Prevent session fixation attacks
ini_set('session.use_strict_mode', 1);

// Set SameSite attribute
ini_set('session.cookie_samesite', 'Strict');

// Regenerate session ID on login
session_regenerate_id(true);
```

**Q5: How do you implement session timeout in PHP?**
A: Session timeout implementation:
```php
session_start();

// Set timeout duration (30 minutes)
$timeout_duration = 1800;

// Check if session has timed out
if (isset($_SESSION['last_activity']) && 
    (time() - $_SESSION['last_activity']) > $timeout_duration) {
    
    // Session has expired
    session_unset();
    session_destroy();
    session_start();
    session_regenerate_id(true);
    
    // Redirect to login
    header('Location: login.php');
    exit();
}

// Update last activity timestamp
$_SESSION['last_activity'] = time();
```

**Q6: What is session fixation and how do you prevent it?**
A: Session fixation is when an attacker sets a user's session ID to a known value. Prevention:
```php
// Always regenerate session ID on login
function secureLogin($username, $password) {
    if (validateCredentials($username, $password)) {
        // Regenerate session ID to prevent fixation
        session_regenerate_id(true);
        
        $_SESSION['user_id'] = getUserId($username);
        $_SESSION['username'] = $username;
        $_SESSION['login_time'] = time();
        
        return true;
    }
    return false;
}

// Use strict mode to reject uninitialized session IDs
ini_set('session.use_strict_mode', 1);
```

### Advanced Session Management

**Q7: How do you implement custom session handlers?**
A: Custom session handler implementation:
```php
class DatabaseSessionHandler implements SessionHandlerInterface {
    private $pdo;
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }
    
    public function open($savePath, $sessionName) {
        return true;
    }
    
    public function close() {
        return true;
    }
    
    public function read($sessionId) {
        $stmt = $this->pdo->prepare("SELECT data FROM sessions WHERE id = ?");
        $stmt->execute([$sessionId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['data'] : '';
    }
    
    public function write($sessionId, $data) {
        $stmt = $this->pdo->prepare("
            INSERT INTO sessions (id, data, timestamp) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE data = ?, timestamp = ?
        ");
        $timestamp = time();
        return $stmt->execute([$sessionId, $data, $timestamp, $data, $timestamp]);
    }
    
    public function destroy($sessionId) {
        $stmt = $this->pdo->prepare("DELETE FROM sessions WHERE id = ?");
        return $stmt->execute([$sessionId]);
    }
    
    public function gc($maxlifetime) {
        $stmt = $this->pdo->prepare("DELETE FROM sessions WHERE timestamp < ?");
        return $stmt->execute([time() - $maxlifetime]);
    }
}

// Register custom handler
$handler = new DatabaseSessionHandler($pdo);
session_set_save_handler($handler, true);
session_start();
```

**Q8: How do you implement secure user authentication with sessions?**
A: Secure authentication system:
```php
class SecureAuth {
    private $pdo;
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
        $this->configureSession();
    }
    
    private function configureSession() {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        session_start();
    }
    
    public function login($email, $password) {
        $stmt = $this->pdo->prepare("
            SELECT id, password, failed_attempts, locked_until 
            FROM users WHERE email = ? AND status = 'active'
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        // Check if account is locked
        if ($user['locked_until'] && time() < $user['locked_until']) {
            return ['success' => false, 'message' => 'Account temporarily locked'];
        }
        
        // Verify password
        if (password_verify($password, $user['password'])) {
            // Reset failed attempts
            $this->resetFailedAttempts($user['id']);
            
            // Regenerate session ID
            session_regenerate_id(true);
            
            // Set session data
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['email'] = $email;
            $_SESSION['login_time'] = time();
            $_SESSION['last_activity'] = time();
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            return ['success' => true, 'message' => 'Login successful'];
        } else {
            // Increment failed attempts
            $this->incrementFailedAttempts($user['id']);
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
    }
    
    public function logout() {
        session_unset();
        session_destroy();
        session_start();
        session_regenerate_id(true);
    }
    
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && $this->validateSession();
    }
    
    private function validateSession() {
        // Check session timeout
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity']) > 1800) {
            $this->logout();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    private function incrementFailedAttempts($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE users 
            SET failed_attempts = failed_attempts + 1,
                locked_until = CASE 
                    WHEN failed_attempts >= 4 THEN ? 
                    ELSE NULL 
                END
            WHERE id = ?
        ");
        $lockUntil = time() + 900; // Lock for 15 minutes
        $stmt->execute([$lockUntil, $userId]);
    }
    
    private function resetFailedAttempts($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE users 
            SET failed_attempts = 0, locked_until = NULL 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
    }
    
    private function updateLastLogin($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE users SET last_login = NOW() WHERE id = ?
        ");
        $stmt->execute([$userId]);
    }
}
```

## PDO QUESTIONS

### PDO Fundamentals

**Q9: What is PDO and what are its advantages over mysqli?**
A: PDO (PHP Data Objects) advantages:
- **Database abstraction**: Works with multiple database types (MySQL, PostgreSQL, SQLite, etc.)
- **Object-oriented interface**: Consistent API across databases
- **Prepared statements**: Built-in SQL injection protection
- **Exception handling**: Better error management
- **Fetch modes**: Flexible data retrieval options
- **Transaction support**: ACID compliance

**Q10: How do you establish a secure PDO connection?**
A: Secure PDO connection setup:
```php
class Database {
    private static $instance = null;
    private $pdo;
    
    private function __construct() {
        $host = 'localhost';
        $dbname = 'your_database';
        $username = 'your_username';
        $password = 'your_password';
        
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_PERSISTENT => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ];
        
        try {
            $this->pdo = new PDO($dsn, $username, $password, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->pdo;
    }
}
```

### PDO Prepared Statements and Security

**Q11: Why are prepared statements important and how do they prevent SQL injection?**
A: Prepared statements prevent SQL injection by:
- **Separating SQL logic from data**: Query structure is defined first
- **Parameter binding**: Data is sent separately and safely escaped
- **No string concatenation**: Eliminates injection attack vectors
- **Type checking**: Parameters are validated by database

Example of vulnerable vs secure code:
```php
// VULNERABLE - Never do this!
$sql = "SELECT * FROM users WHERE email = '" . $_POST['email'] . "'";
$result = $pdo->query($sql);

// SECURE - Always use prepared statements
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
$stmt->execute([$_POST['email']]);
$result = $stmt->fetchAll();
```

**Q12: What's the difference between named and positional parameters in PDO?**
A: Parameter binding comparison:
```php
// Positional parameters (?)
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = ?");
$stmt->execute([$email, $status]);

// Named parameters (:name)
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = :email AND status = :status");
$stmt->execute([
    ':email' => $email,
    ':status' => $status
]);

// Named parameters are more readable for complex queries
$stmt = $pdo->prepare("
    UPDATE users
    SET name = :name, email = :email, updated_at = :updated
    WHERE id = :id AND status = :status
");
$stmt->execute([
    ':name' => $name,
    ':email' => $email,
    ':updated' => date('Y-m-d H:i:s'),
    ':id' => $userId,
    ':status' => 'active'
]);
```

**Q13: How do you handle different PDO fetch modes?**
A: PDO fetch modes explained:
```php
$stmt = $pdo->prepare("SELECT id, name, email FROM users WHERE status = ?");
$stmt->execute(['active']);

// FETCH_ASSOC - Associative array
$user = $stmt->fetch(PDO::FETCH_ASSOC);
// Result: ['id' => 1, 'name' => 'John', 'email' => '<EMAIL>']

// FETCH_NUM - Numeric array
$user = $stmt->fetch(PDO::FETCH_NUM);
// Result: [1, 'John', '<EMAIL>']

// FETCH_OBJ - Anonymous object
$user = $stmt->fetch(PDO::FETCH_OBJ);
// Result: object with properties $user->id, $user->name, $user->email

// FETCH_CLASS - Specific class instance
class User {
    public $id, $name, $email;
}
$stmt->setFetchMode(PDO::FETCH_CLASS, 'User');
$user = $stmt->fetch();

// FETCH_COLUMN - Single column value
$stmt = $pdo->prepare("SELECT COUNT(*) FROM users");
$stmt->execute();
$count = $stmt->fetchColumn();

// FETCH_KEY_PAIR - Key-value pairs
$stmt = $pdo->prepare("SELECT id, name FROM users");
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
// Result: [1 => 'John', 2 => 'Jane', ...]
```

### PDO Transactions and Error Handling

**Q14: How do you implement database transactions with PDO?**
A: Transaction implementation:
```php
class OrderService {
    private $pdo;

    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }

    public function createOrder($userId, $items) {
        try {
            // Start transaction
            $this->pdo->beginTransaction();

            // Create order record
            $stmt = $this->pdo->prepare("
                INSERT INTO orders (user_id, total, status, created_at)
                VALUES (?, ?, 'pending', NOW())
            ");
            $total = $this->calculateTotal($items);
            $stmt->execute([$userId, $total]);
            $orderId = $this->pdo->lastInsertId();

            // Add order items
            $stmt = $this->pdo->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, price)
                VALUES (?, ?, ?, ?)
            ");

            foreach ($items as $item) {
                // Check stock availability
                if (!$this->checkStock($item['product_id'], $item['quantity'])) {
                    throw new Exception("Insufficient stock for product " . $item['product_id']);
                }

                $stmt->execute([
                    $orderId,
                    $item['product_id'],
                    $item['quantity'],
                    $item['price']
                ]);

                // Update stock
                $this->updateStock($item['product_id'], $item['quantity']);
            }

            // Update order status
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'confirmed' WHERE id = ?");
            $stmt->execute([$orderId]);

            // Commit transaction
            $this->pdo->commit();

            return ['success' => true, 'order_id' => $orderId];

        } catch (Exception $e) {
            // Rollback on any error
            $this->pdo->rollback();
            error_log("Order creation failed: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function checkStock($productId, $quantity) {
        $stmt = $this->pdo->prepare("SELECT stock FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        $stock = $stmt->fetchColumn();
        return $stock >= $quantity;
    }

    private function updateStock($productId, $quantity) {
        $stmt = $this->pdo->prepare("
            UPDATE products
            SET stock = stock - ?
            WHERE id = ?
        ");
        $stmt->execute([$quantity, $productId]);
    }

    private function calculateTotal($items) {
        return array_sum(array_map(function($item) {
            return $item['price'] * $item['quantity'];
        }, $items));
    }
}
```

**Q15: How do you handle PDO errors and exceptions properly?**
A: Comprehensive error handling:
```php
class DatabaseManager {
    private $pdo;

    public function __construct() {
        try {
            $this->pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            $this->logError("Connection failed", $e);
            throw new Exception("Database connection failed");
        }
    }

    public function executeQuery($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            $this->handleDatabaseError($e, $sql, $params);
            throw new Exception("Query execution failed");
        }
    }

    private function handleDatabaseError(PDOException $e, $sql, $params) {
        $errorInfo = [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'sql' => $sql,
            'params' => $params,
            'trace' => $e->getTraceAsString()
        ];

        // Log different types of errors
        switch ($e->getCode()) {
            case '23000': // Integrity constraint violation
                $this->logError("Constraint violation", $e, $errorInfo);
                break;
            case '42S02': // Table doesn't exist
                $this->logError("Table not found", $e, $errorInfo);
                break;
            case '42000': // Syntax error
                $this->logError("SQL syntax error", $e, $errorInfo);
                break;
            default:
                $this->logError("Database error", $e, $errorInfo);
        }
    }

    private function logError($type, PDOException $e, $context = []) {
        $logMessage = sprintf(
            "[%s] %s: %s | Context: %s",
            date('Y-m-d H:i:s'),
            $type,
            $e->getMessage(),
            json_encode($context)
        );

        error_log($logMessage, 3, '/var/log/php/database_errors.log');

        // In development, you might want to display errors
        if (defined('DEBUG') && DEBUG) {
            echo "<pre>$logMessage</pre>";
        }
    }
}
```

### Advanced PDO Patterns

**Q16: How do you implement a Repository pattern with PDO?**
A: Repository pattern implementation:
```php
interface UserRepositoryInterface {
    public function findById($id);
    public function findByEmail($email);
    public function create(array $userData);
    public function update($id, array $userData);
    public function delete($id);
    public function findAll($limit = 100, $offset = 0);
}

class UserRepository implements UserRepositoryInterface {
    private $pdo;
    private $table = 'users';

    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }

    public function findById($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM {$this->table} WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    public function findByEmail($email) {
        $stmt = $this->pdo->prepare("SELECT * FROM {$this->table} WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }

    public function create(array $userData) {
        $fields = array_keys($userData);
        $placeholders = ':' . implode(', :', $fields);
        $fieldList = implode(', ', $fields);

        $sql = "INSERT INTO {$this->table} ($fieldList) VALUES ($placeholders)";
        $stmt = $this->pdo->prepare($sql);

        // Add colon prefix to keys for named parameters
        $params = [];
        foreach ($userData as $key => $value) {
            $params[':' . $key] = $value;
        }

        $stmt->execute($params);
        return $this->pdo->lastInsertId();
    }

    public function update($id, array $userData) {
        $fields = [];
        $params = [':id' => $id];

        foreach ($userData as $key => $value) {
            $fields[] = "$key = :$key";
            $params[':' . $key] = $value;
        }

        $fieldList = implode(', ', $fields);
        $sql = "UPDATE {$this->table} SET $fieldList WHERE id = :id";

        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $stmt = $this->pdo->prepare("DELETE FROM {$this->table} WHERE id = ?");
        return $stmt->execute([$id]);
    }

    public function findAll($limit = 100, $offset = 0) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM {$this->table}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        return $stmt->fetchAll();
    }

    public function findByStatus($status) {
        $stmt = $this->pdo->prepare("SELECT * FROM {$this->table} WHERE status = ?");
        $stmt->execute([$status]);
        return $stmt->fetchAll();
    }

    public function search($term, $limit = 50) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM {$this->table}
            WHERE name LIKE ? OR email LIKE ?
            ORDER BY name
            LIMIT ?
        ");
        $searchTerm = "%$term%";
        $stmt->execute([$searchTerm, $searchTerm, $limit]);
        return $stmt->fetchAll();
    }
}
```

## REAL-WORLD SCENARIOS

**Q17: How do you implement a complete user registration and login system?**
A: Complete authentication system:
```php
class AuthenticationSystem {
    private $pdo;
    private $sessionTimeout = 1800; // 30 minutes

    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
        $this->initializeSession();
    }

    private function initializeSession() {
        // Secure session configuration
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');
        session_start();
    }

    public function register($email, $password, $name) {
        try {
            // Validate input
            if (!$this->validateEmail($email)) {
                return ['success' => false, 'message' => 'Invalid email format'];
            }

            if (!$this->validatePassword($password)) {
                return ['success' => false, 'message' => 'Password must be at least 8 characters with uppercase, lowercase, and number'];
            }

            // Check if user already exists
            if ($this->userExists($email)) {
                return ['success' => false, 'message' => 'User already exists'];
            }

            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Create user
            $stmt = $this->pdo->prepare("
                INSERT INTO users (email, password, name, status, created_at, email_verified)
                VALUES (?, ?, ?, 'active', NOW(), 0)
            ");
            $stmt->execute([$email, $hashedPassword, $name]);

            $userId = $this->pdo->lastInsertId();

            // Send verification email (implementation depends on your email service)
            $this->sendVerificationEmail($email, $userId);

            return ['success' => true, 'message' => 'Registration successful. Please check your email for verification.'];

        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Registration failed'];
        }
    }

    public function login($email, $password, $rememberMe = false) {
        try {
            // Get user data
            $stmt = $this->pdo->prepare("
                SELECT id, email, password, name, status, failed_attempts, locked_until
                FROM users WHERE email = ?
            ");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if (!$user) {
                return ['success' => false, 'message' => 'Invalid credentials'];
            }

            // Check account status
            if ($user['status'] !== 'active') {
                return ['success' => false, 'message' => 'Account is not active'];
            }

            // Check if account is locked
            if ($user['locked_until'] && time() < $user['locked_until']) {
                $remainingTime = ceil(($user['locked_until'] - time()) / 60);
                return ['success' => false, 'message' => "Account locked for $remainingTime minutes"];
            }

            // Verify password
            if (!password_verify($password, $user['password'])) {
                $this->handleFailedLogin($user['id']);
                return ['success' => false, 'message' => 'Invalid credentials'];
            }

            // Successful login
            $this->handleSuccessfulLogin($user, $rememberMe);

            return ['success' => true, 'message' => 'Login successful'];

        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Login failed'];
        }
    }

    private function handleSuccessfulLogin($user, $rememberMe) {
        // Reset failed attempts
        $stmt = $this->pdo->prepare("
            UPDATE users
            SET failed_attempts = 0, locked_until = NULL, last_login = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$user['id']]);

        // Regenerate session ID
        session_regenerate_id(true);

        // Set session data
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['name'] = $user['name'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();

        // Handle "Remember Me" functionality
        if ($rememberMe) {
            $this->setRememberMeToken($user['id']);
        }
    }

    private function handleFailedLogin($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE users
            SET failed_attempts = failed_attempts + 1,
                locked_until = CASE
                    WHEN failed_attempts >= 4 THEN ?
                    ELSE NULL
                END
            WHERE id = ?
        ");
        $lockUntil = time() + 900; // Lock for 15 minutes after 5 failed attempts
        $stmt->execute([$lockUntil, $userId]);
    }

    private function setRememberMeToken($userId) {
        $token = bin2hex(random_bytes(32));
        $expires = time() + (30 * 24 * 60 * 60); // 30 days

        // Store token in database
        $stmt = $this->pdo->prepare("
            INSERT INTO remember_tokens (user_id, token, expires_at)
            VALUES (?, ?, FROM_UNIXTIME(?))
            ON DUPLICATE KEY UPDATE token = ?, expires_at = FROM_UNIXTIME(?)
        ");
        $stmt->execute([$userId, hash('sha256', $token), $expires, hash('sha256', $token), $expires]);

        // Set cookie
        setcookie('remember_token', $token, $expires, '/', '', true, true);
    }

    public function checkRememberMe() {
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            $hashedToken = hash('sha256', $token);

            $stmt = $this->pdo->prepare("
                SELECT u.id, u.email, u.name
                FROM users u
                JOIN remember_tokens rt ON u.id = rt.user_id
                WHERE rt.token = ? AND rt.expires_at > NOW() AND u.status = 'active'
            ");
            $stmt->execute([$hashedToken]);
            $user = $stmt->fetch();

            if ($user) {
                // Auto-login user
                session_regenerate_id(true);
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['name'] = $user['name'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();

                return true;
            } else {
                // Invalid token, remove cookie
                setcookie('remember_token', '', time() - 3600, '/', '', true, true);
            }
        }
        return false;
    }

    public function logout() {
        // Remove remember me token if exists
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            $hashedToken = hash('sha256', $token);

            $stmt = $this->pdo->prepare("DELETE FROM remember_tokens WHERE token = ?");
            $stmt->execute([$hashedToken]);

            setcookie('remember_token', '', time() - 3600, '/', '', true, true);
        }

        // Destroy session
        session_unset();
        session_destroy();
        session_start();
        session_regenerate_id(true);
    }

    public function isLoggedIn() {
        if (!isset($_SESSION['user_id'])) {
            return $this->checkRememberMe();
        }

        // Check session timeout
        if (isset($_SESSION['last_activity']) &&
            (time() - $_SESSION['last_activity']) > $this->sessionTimeout) {
            $this->logout();
            return false;
        }

        $_SESSION['last_activity'] = time();
        return true;
    }

    private function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    private function validatePassword($password) {
        return strlen($password) >= 8 &&
               preg_match('/[A-Z]/', $password) &&
               preg_match('/[a-z]/', $password) &&
               preg_match('/[0-9]/', $password);
    }

    private function userExists($email) {
        $stmt = $this->pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch() !== false;
    }

    private function sendVerificationEmail($email, $userId) {
        // Implementation depends on your email service
        // Generate verification token and send email
    }
}
```

**Q18: How do you implement pagination with PDO?**
A: Advanced pagination implementation:
```php
class Paginator {
    private $pdo;
    private $table;
    private $perPage;
    private $currentPage;
    private $totalRecords;
    private $conditions = [];
    private $orderBy = '';

    public function __construct(PDO $pdo, $table, $perPage = 10) {
        $this->pdo = $pdo;
        $this->table = $table;
        $this->perPage = $perPage;
    }

    public function where($column, $operator, $value) {
        $this->conditions[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value
        ];
        return $this;
    }

    public function orderBy($column, $direction = 'ASC') {
        $this->orderBy = "ORDER BY $column $direction";
        return $this;
    }

    public function paginate($page = 1) {
        $this->currentPage = max(1, (int)$page);
        $offset = ($this->currentPage - 1) * $this->perPage;

        // Build WHERE clause
        $whereClause = '';
        $params = [];
        if (!empty($this->conditions)) {
            $conditions = [];
            foreach ($this->conditions as $condition) {
                $conditions[] = "{$condition['column']} {$condition['operator']} ?";
                $params[] = $condition['value'];
            }
            $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        }

        // Count total records
        $countSql = "SELECT COUNT(*) FROM {$this->table} $whereClause";
        $stmt = $this->pdo->prepare($countSql);
        $stmt->execute($params);
        $this->totalRecords = $stmt->fetchColumn();

        // Get paginated data
        $dataSql = "SELECT * FROM {$this->table} $whereClause {$this->orderBy} LIMIT ? OFFSET ?";
        $params[] = $this->perPage;
        $params[] = $offset;

        $stmt = $this->pdo->prepare($dataSql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();

        return [
            'data' => $data,
            'pagination' => $this->getPaginationInfo()
        ];
    }

    private function getPaginationInfo() {
        $totalPages = ceil($this->totalRecords / $this->perPage);

        return [
            'current_page' => $this->currentPage,
            'per_page' => $this->perPage,
            'total_records' => $this->totalRecords,
            'total_pages' => $totalPages,
            'has_previous' => $this->currentPage > 1,
            'has_next' => $this->currentPage < $totalPages,
            'previous_page' => $this->currentPage > 1 ? $this->currentPage - 1 : null,
            'next_page' => $this->currentPage < $totalPages ? $this->currentPage + 1 : null,
            'first_page' => 1,
            'last_page' => $totalPages
        ];
    }

    public function generatePaginationLinks($baseUrl, $range = 2) {
        $pagination = $this->getPaginationInfo();
        $links = [];

        // Previous link
        if ($pagination['has_previous']) {
            $links['previous'] = $baseUrl . '?page=' . $pagination['previous_page'];
        }

        // Page number links
        $start = max(1, $pagination['current_page'] - $range);
        $end = min($pagination['total_pages'], $pagination['current_page'] + $range);

        for ($i = $start; $i <= $end; $i++) {
            $links['pages'][$i] = [
                'url' => $baseUrl . '?page=' . $i,
                'current' => $i === $pagination['current_page']
            ];
        }

        // Next link
        if ($pagination['has_next']) {
            $links['next'] = $baseUrl . '?page=' . $pagination['next_page'];
        }

        return $links;
    }
}

// Usage example
$paginator = new Paginator($pdo, 'users', 10);
$result = $paginator
    ->where('status', '=', 'active')
    ->where('created_at', '>', '2023-01-01')
    ->orderBy('created_at', 'DESC')
    ->paginate($_GET['page'] ?? 1);

$users = $result['data'];
$pagination = $result['pagination'];
$links = $paginator->generatePaginationLinks('/users');
```

## INTERVIEW-STYLE QUESTIONS

**Q19: Explain the difference between session_destroy() and session_unset().**
A:
- **`session_unset()`**: Removes all session variables but keeps the session active
- **`session_destroy()`**: Destroys all session data and ends the session
- **Best practice**: Use both together for complete logout
```php
// Complete session cleanup
session_unset();     // Clear session variables
session_destroy();   // Destroy session data
session_start();     // Start fresh session
session_regenerate_id(true); // New session ID
```

**Q20: What are the security implications of using emulated prepares in PDO?**
A: Emulated prepares security considerations:
- **`PDO::ATTR_EMULATE_PREPARES = true`**: PDO builds the query string (potential injection risk)
- **`PDO::ATTR_EMULATE_PREPARES = false`**: Database handles preparation (more secure)
- **Recommendation**: Always set to `false` for better security
```php
$pdo = new PDO($dsn, $user, $pass, [
    PDO::ATTR_EMULATE_PREPARES => false, // Use real prepared statements
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
]);
```

**Q21: How do you handle concurrent session access in PHP?**
A: Concurrent session handling strategies:
```php
// Problem: Session locking blocks concurrent requests
session_start();
// Long-running operation blocks other requests

// Solution 1: Close session early
session_start();
$data = $_SESSION['data'];
session_write_close(); // Release session lock
// Continue with long operation

// Solution 2: Read-only session access
session_start();
$readonly_data = $_SESSION;
session_write_close();
// Use $readonly_data instead of $_SESSION

// Solution 3: Custom session handler with row-level locking
class NonBlockingSessionHandler implements SessionHandlerInterface {
    // Implementation with SELECT ... FOR UPDATE NOWAIT
}
```

**Q22: What's the difference between PDO::FETCH_ASSOC and PDO::FETCH_ARRAY?**
A: Fetch mode comparison:
```php
// FETCH_ASSOC: Only associative keys
$user = $stmt->fetch(PDO::FETCH_ASSOC);
// Result: ['id' => 1, 'name' => 'John']

// FETCH_ARRAY (same as FETCH_BOTH): Both numeric and associative
$user = $stmt->fetch(PDO::FETCH_ARRAY);
// Result: [0 => 1, 'id' => 1, 1 => 'John', 'name' => 'John']

// FETCH_NUM: Only numeric keys
$user = $stmt->fetch(PDO::FETCH_NUM);
// Result: [0 => 1, 1 => 'John']

// Performance: FETCH_ASSOC is more memory efficient
```

## DEBUGGING AND TROUBLESHOOTING

**Q23: How do you debug PDO connection issues?**
A: PDO debugging techniques:
```php
// Enable detailed error reporting
try {
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 5, // Connection timeout
    ]);

    // Test connection
    $pdo->query('SELECT 1');
    echo "Connection successful";

} catch (PDOException $e) {
    // Log detailed error information
    error_log("PDO Connection Error: " . $e->getMessage());
    error_log("Error Code: " . $e->getCode());
    error_log("DSN: " . $dsn);

    // Check common issues
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "Check username/password";
    } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "Check database name";
    } elseif (strpos($e->getMessage(), "Can't connect") !== false) {
        echo "Check host/port";
    }

    throw new Exception("Database connection failed");
}
```

**Q24: How do you troubleshoot session problems?**
A: Session troubleshooting checklist:
```php
// Debug session configuration
function debugSession() {
    echo "Session Status: " . session_status() . "\n";
    echo "Session ID: " . session_id() . "\n";
    echo "Session Name: " . session_name() . "\n";
    echo "Session Save Path: " . session_save_path() . "\n";
    echo "Session Cookie Params: " . print_r(session_get_cookie_params(), true) . "\n";

    // Check if sessions are working
    if (session_status() === PHP_SESSION_NONE) {
        echo "Sessions not started\n";
    }

    // Check session data
    echo "Session Data: " . print_r($_SESSION, true) . "\n";

    // Check headers
    if (headers_sent($file, $line)) {
        echo "Headers already sent in $file on line $line\n";
    }
}

// Common session issues and solutions
// 1. Headers already sent
if (!headers_sent()) {
    session_start();
} else {
    echo "Cannot start session - headers already sent";
}

// 2. Session data not persisting
// Check session.save_path permissions
$savePath = session_save_path();
if (!is_writable($savePath)) {
    echo "Session save path not writable: $savePath";
}

// 3. Session hijacking prevention
function validateSession() {
    if (!isset($_SESSION['user_agent'])) {
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
    }

    if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
        session_destroy();
        return false;
    }

    return true;
}
```

## PERFORMANCE OPTIMIZATION

**Q25: How do you optimize PDO performance?**
A: PDO performance optimization strategies:
```php
// 1. Use persistent connections (carefully)
$pdo = new PDO($dsn, $user, $pass, [
    PDO::ATTR_PERSISTENT => true, // Reuse connections
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
]);

// 2. Prepare statements once, execute multiple times
$stmt = $pdo->prepare("INSERT INTO users (name, email) VALUES (?, ?)");
foreach ($users as $user) {
    $stmt->execute([$user['name'], $user['email']]);
}

// 3. Use transactions for bulk operations
$pdo->beginTransaction();
try {
    $stmt = $pdo->prepare("INSERT INTO logs (message, created_at) VALUES (?, NOW())");
    foreach ($logs as $log) {
        $stmt->execute([$log]);
    }
    $pdo->commit();
} catch (Exception $e) {
    $pdo->rollback();
    throw $e;
}

// 4. Optimize fetch modes
$stmt->setFetchMode(PDO::FETCH_ASSOC); // Set once
while ($row = $stmt->fetch()) {
    // Process row
}

// 5. Use LIMIT for large datasets
$stmt = $pdo->prepare("SELECT * FROM large_table LIMIT ? OFFSET ?");
$stmt->execute([100, $offset]);

// 6. Connection pooling simulation
class ConnectionPool {
    private static $connections = [];
    private static $maxConnections = 10;

    public static function getConnection() {
        if (count(self::$connections) < self::$maxConnections) {
            $pdo = new PDO($dsn, $user, $pass);
            self::$connections[] = $pdo;
            return $pdo;
        }

        // Return existing connection
        return self::$connections[array_rand(self::$connections)];
    }
}
```

**Q26: How do you implement session-based caching?**
A: Session caching implementation:
```php
class SessionCache {
    private static $cacheTimeout = 300; // 5 minutes

    public static function get($key) {
        if (!isset($_SESSION['cache'][$key])) {
            return null;
        }

        $cached = $_SESSION['cache'][$key];

        // Check if cache has expired
        if (time() > $cached['expires']) {
            unset($_SESSION['cache'][$key]);
            return null;
        }

        return $cached['data'];
    }

    public static function set($key, $data, $timeout = null) {
        if (!isset($_SESSION['cache'])) {
            $_SESSION['cache'] = [];
        }

        $timeout = $timeout ?: self::$cacheTimeout;

        $_SESSION['cache'][$key] = [
            'data' => $data,
            'expires' => time() + $timeout
        ];
    }

    public static function delete($key) {
        unset($_SESSION['cache'][$key]);
    }

    public static function clear() {
        $_SESSION['cache'] = [];
    }

    public static function cleanup() {
        if (!isset($_SESSION['cache'])) {
            return;
        }

        $now = time();
        foreach ($_SESSION['cache'] as $key => $cached) {
            if ($now > $cached['expires']) {
                unset($_SESSION['cache'][$key]);
            }
        }
    }
}

// Usage
SessionCache::set('user_preferences', $preferences, 600);
$preferences = SessionCache::get('user_preferences');
```

This comprehensive PHP Session Management & PDO Q&A guide covers fundamental concepts through advanced real-world implementations, security best practices, debugging techniques, and performance optimization strategies for professional PHP development.
