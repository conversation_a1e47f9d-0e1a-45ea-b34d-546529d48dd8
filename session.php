<?php
// Include the configuration file that contains session settings
require_once 'config.php';

/**
 * Session Class - Handles all session operations securely
 * 
 * This class provides methods for working with PHP sessions in a secure way,
 * including protection against session fixation and hijacking attacks.
 */
class Session {
    /**
     * Initialize the session with security checks
     * This should be called at the beginning of each script that uses sessions
     */
    public static function init() {
        // Start the session if it's not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // ===== SESSION FIXATION PROTECTION =====
        // Regenerate session ID periodically to prevent session fixation attacks
        // Session fixation is when an attacker sets a known session ID and tricks a user into using it
        if (!isset($_SESSION['created'])) {
            // For new sessions, record the creation time
            $_SESSION['created'] = time();
        } else if (time() - $_SESSION['created'] > 1800) { // 30 minutes
            // For existing sessions, regenerate the ID every 30 minutes
            session_regenerate_id(true);  // true means delete the old session file
            $_SESSION['created'] = time(); // Reset the creation time
        }
        
        // ===== SESSION HIJACKING PROTECTION =====
        // Check for session hijacking by comparing the user's browser fingerprint
        // Session hijacking is when an attacker steals a session ID and uses it
        if (!isset($_SESSION['user_agent'])) {
            // For new sessions, store the user agent string
            $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
        } else if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
            // If the user agent changes during a session, it might be hijacking
            self::destroy(); // Destroy the session
            exit('Session error: possible hijacking attempt'); // Stop execution with an error message
        }
    }
    
    /**
     * Set a session variable
     * 
     * @param string $key The session variable name
     * @param mixed $value The value to store
     */
    public static function set($key, $value) {
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get a session variable
     * 
     * @param string $key The session variable name
     * @return mixed The session variable value or null if not set
     */
    public static function get($key) {
        return isset($_SESSION[$key]) ? $_SESSION[$key] : null;
    }
    
    /**
     * Check if a session variable exists
     * 
     * @param string $key The session variable name
     * @return bool True if the variable exists, false otherwise
     */
    public static function exists($key) {
        return isset($_SESSION[$key]);
    }
    
    /**
     * Remove a session variable
     * 
     * @param string $key The session variable name
     * @return bool True if the variable was removed, false if it didn't exist
     */
    public static function remove($key) {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
            return true;
        }
        return false;
    }
    
    /**
     * Destroy the session completely
     * This removes all session data and the session cookie
     */
    public static function destroy() {
        // Unset all session variables by replacing $_SESSION with an empty array
        $_SESSION = [];
        
        // Delete the session cookie by setting its expiration time in the past
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),        // Cookie name (usually 'PHPSESSID')
                '',                     // Cookie value (empty to delete)
                time() - 42000,        // Expiration time (in the past)
                $params['path'],        // Cookie path
                $params['domain'],      // Cookie domain
                $params['secure'],      // Secure flag
                $params['httponly']     // HttpOnly flag
            );
        }
        
        // Finally, destroy the session data on the server
        session_destroy();
    }
    
    /**
     * Set a flash message (available only for the next request)
     * Flash messages are useful for one-time notifications like "Login successful"
     * 
     * @param string $key The flash message key
     * @param mixed $message The message content
     */
    public static function setFlash($key, $message) {
        $_SESSION['flash'][$key] = $message;
    }
    
    /**
     * Get a flash message and remove it
     * 
     * @param string $key The flash message key
     * @return mixed The message content or null if not set
     */
    public static function getFlash($key) {
        $message = null;
        if (isset($_SESSION['flash'][$key])) {
            $message = $_SESSION['flash'][$key];
            unset($_SESSION['flash'][$key]);
        }
        return $message;
    }
    
    /**
     * Check if a flash message exists
     * 
     * @param string $key The flash message key
     * @return bool True if the flash message exists, false otherwise
     */
    public static function hasFlash($key) {
        return isset($_SESSION['flash'][$key]);
    }
}
?>